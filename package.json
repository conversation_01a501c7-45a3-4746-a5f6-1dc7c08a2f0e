{"private": true, "author": "SissleLynn <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@umijs/max": "^4.4.6", "antd": "^5.4.0", "classnames": "^2.5.1", "cos-js-sdk-v5": "^1.8.7", "dayjs": "^1.11.13", "echarts": "^5.6.0"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}
.assessorPage {
  .itemHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding: 16px 24px 12px;

    .endTime {
      margin-top: 10px;
    }

    h1,
    p {
      margin: 0;
    }

    :global {
      .ant-statistic-title {
        color: #000;
      }

      .ant-statistic-content {
        color: #4f46e5;
      }
    }
  }

  .itemContent {
    margin-top: 24px;

    .itemCard {
      display: flex;
      align-items: center;
      justify-content: space-between;

      h2 {
        font-size: 26px;
        margin: 0;
      }

      > div:nth-child(2) {
        width: 50px;
        height: 50px;
        background-color: #dbeafe;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #06f;
      }

      &.green {
        > div:nth-child(2) {
          background-color: #d1fadf;
          color: #059669;
        }
      }

      &.yellow {
        > div:nth-child(2) {
          background-color: #fef3c7;
          color: #d97706;
        }
      }
    }

    .searchWrapper {
      margin: 24px 0;
    }

    .peopleCard {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      h2 {
        align-self: stretch;
        color: #000;
        font-family: 'PingFang SC';
        font-size: 18px;
        font-weight: normal;
        margin: 0;
      }

      .descInfo {
        margin: 0;
        line-height: 30px;
        color: #4b5563;

        span {
          margin-right: 8px;
        }
      }

      button {
        width: 100%;
        margin-top: 16px;
      }
    }
  }
}
@media (max-width: 768px) {
  .assessorPage {
    background: #f1f1f1;

    .itemContent {
      padding: 0 16px;
    }
  }
}

import { PageContainer } from '@ant-design/pro-components';
import { Tabs, TabsProps } from 'antd';
import PersonalList from './PersonalList';
import PublicRecord from './PublicRecord';

const QueryEvaluation = () => {
  const items: TabsProps['items'] = [
    {
      key: 'personalList',
      label: '个人查询',
      children: <PersonalList />,
    },
    {
      key: 'publicRecord',
      label: '考核公示',
      children: <PublicRecord />,
    },
  ];
  return (
    <PageContainer
      ghost
      header={{
        title: false,
        breadcrumb: {},
      }}
    >
      <Tabs defaultActiveKey="personalList" items={items} />
    </PageContainer>
  );
};

export default QueryEvaluation;

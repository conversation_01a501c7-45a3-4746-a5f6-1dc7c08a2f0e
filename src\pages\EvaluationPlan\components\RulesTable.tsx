import {
  checkEvaluationRuleAPI,
  createEvaluationRuleAPI,
  getAllEvaluationRuleAPI,
  removeEvaluationRuleAPI,
  updateEvaluationRuleAPI,
} from '@/services/evaluation_rule';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AddScoreRuleModal from './AddScoreRuleModal';
import { Level, ruleColumns } from './columns';
import styles from './index.less';

interface RulesTablePros {
  planId?: number;
  title?: string;
  handleTemplateClick: ({
    next,
    prev,
    data,
  }: {
    next?: Level;
    prev?: Level;
    data?: API.IRule;
  }) => void;
  readonly: boolean;
}

const RulesTable: React.FC<RulesTablePros> = ({
  planId,
  title,
  handleTemplateClick,
  readonly,
}) => {
  const [scoreRuleModalOpen, setScoreRuleModalOpen] = useState<{
    open: boolean;
    data?: API.IRule;
  }>({
    open: false,
    data: undefined,
  });
  const [dataSource, setDataSource] = useState<API.IRule[]>([]);
  /** 查询考核方案是否配置完整考核规则  */
  const [checkRulesId, setCheckRulesId] = useState<number[]>([]);
  const [checkingStatus, setCheckingStatus] = useState<
    Record<
      string,
      {
        status: 'checking' | 'complete' | 'incomplete' | 'error';
        retry?: () => void;
      }
    >
  >({});

  /** 获取考核列表 */
  const getAllEvaluationRuleList = async (isCheck = false) => {
    if (!planId) return { data: [], success: false };
    const { errCode, msg, data } = await getAllEvaluationRuleAPI(planId);

    if (errCode) {
      message.warning('获取考核列表数据失败，' + msg);
      setDataSource([]);
      return;
    }

    if (data?.list && data?.list?.length) {
      const initialList = data.list.map((item: any) => {
        // 保留之前的规则检测状态
        const existingItem: any = dataSource.find(
          (ds) => ds.ruleId === item.ruleId,
        );
        const rulesStatus =
          existingItem?.rulesStatus || (isCheck ? 'checking' : 'unchecked');
        const rules = existingItem?.rules || (isCheck ? '检查中...' : '未检查');
        return {
          ...item,
          rules,
          rulesStatus,
        };
      });

      if (isCheck) {
        const ruleIds = initialList.map((item: any) => item?.ruleId);
        setCheckRulesId(ruleIds);
        const initialStatus: Record<
          string,
          {
            status: 'checking' | 'complete' | 'incomplete' | 'error';
            retry?: () => void;
          }
        > = {};
        ruleIds.forEach((ruleId) => {
          initialStatus[ruleId] = { status: 'checking' };
        });
        setCheckingStatus(initialStatus);
      }
      setDataSource(initialList);
    } else {
      setDataSource([]);
    }
  };

  /** 新增 */
  const onAddScheme = async (values?: API.IRule) => {
    if (!planId) return message.warning('新增失败，planId 不存在');
    if (!values) return message.warning('新增失败，新增内容不能为空');
    const { errCode, msg } = await createEvaluationRuleAPI(planId, values);
    if (errCode) {
      message.warning('新增考核规则失败，' + msg);
      return false;
    }
    message.success('新增考核规则成功');
    getAllEvaluationRuleList(true);
    setScoreRuleModalOpen({
      open: false,
      data: undefined,
    });
    return true;
  };

  /** 编辑 */
  const onEditScheme = async (values?: API.IRule) => {
    if (!planId) return message.warning('编辑失败，planId 不存在');
    if (!values?.ruleId) return message.warning('编辑失败，ruleId 不存在');
    if (!values) return message.warning('编辑失败，编辑内容不能为空');
    const { errCode, msg } = await updateEvaluationRuleAPI(
      planId,
      values?.ruleId,
      {
        ...values,
        ruleId: undefined,
        users: values?.assessedType === 'user' ? values?.users : [],
        roles: values?.assessedType === 'role' ? values?.roles : [],
      },
    );
    if (errCode) {
      message.warning('编辑考核规则失败，' + msg);
      return false;
    }
    message.success('编辑考核规则成功');
    setScoreRuleModalOpen({
      open: false,
      data: undefined,
    });
    getAllEvaluationRuleList();
  };

  /** 删除 */
  const onDelete = async (values: API.IRule) => {
    if (!planId) return message.warning('删除失败，planId 不存在');
    if (!values?.ruleId) return message.warning('删除失败，ruleId 不存在');

    const { errCode, msg } = await removeEvaluationRuleAPI(
      planId,
      values.ruleId,
    );
    if (errCode) {
      message.warning('删除考核规则失败，' + msg);
    }
    message.success('删除考核规则成功');
    getAllEvaluationRuleList();
  };

  const updateDataSourceWithStatus = (
    statusMap: Record<string, { status: string; retry?: () => void }>,
  ) => {
    const updatedDataSource = dataSource.map((item) => {
      const status = statusMap[item.ruleId];
      if (!status) return item;
      let rulesText = '检查中...';
      if (status.status === 'complete') {
        rulesText = '规则完整';
      } else if (status.status === 'incomplete') {
        rulesText = '规则不完整';
      } else if (status.status === 'error') {
        rulesText = '检查失败，点击重试';
      }

      return {
        ...item,
        rules: rulesText,
        rulesStatus: status.status,
        retryCheck: status.retry,
      };
    });

    setDataSource(updatedDataSource);
  };

  /** 检查考核规则是否完整 */
  const checkRules = async (ruleIdList: number[]) => {
    if (!ruleIdList?.length || !planId) {
      return message.warning('检查失败，ruleIdList 或 planId 不存在');
    }
    const initialStatus = { ...checkingStatus };
    ruleIdList.forEach((ruleId) => {
      initialStatus[ruleId] = { status: 'checking' };
    });
    setCheckingStatus(initialStatus);
    const checkPromises = ruleIdList.map((ruleId) =>
      checkEvaluationRuleAPI(planId, ruleId),
    );
    try {
      const results = await Promise.allSettled(checkPromises);
      const newStatus = { ...initialStatus };
      results.forEach((result, index) => {
        const ruleId = ruleIdList[index];
        if (result.status === 'rejected') {
          newStatus[ruleId] = {
            status: 'error',
            retry: () => checkRules([ruleId]),
          };
        } else {
          const response = result.value;
          if (response.errCode) {
            newStatus[ruleId] = {
              status: 'error',
              retry: () => checkRules([ruleId]),
            };
          } else {
            const isComplete = !!response.data;
            newStatus[ruleId] = {
              status: isComplete ? 'complete' : 'incomplete',
              retry: isComplete ? undefined : () => checkRules([ruleId]),
            };
          }
        }
      });
      setCheckingStatus(newStatus);
      updateDataSourceWithStatus(newStatus);
    } catch (error) {
      console.error('检查方案完整性时出错:', error);
      return message.warning('检查方案完整性时出错，' + error);
    }
  };

  const handEvent = (type: string, data: API.IRule) => {
    switch (type) {
      case 'remove':
        onDelete(data);
        break;
      case 'edit':
        setScoreRuleModalOpen({ open: true, data });
        break;
      case 'scoring':
        handleTemplateClick({ next: 'scoring', data });
        break;
      case 'retry':
        checkRules([data.ruleId]);
    }
  };

  useEffect(() => {
    if (planId) {
      getAllEvaluationRuleList(true);
    }
  }, [planId]);

  useEffect(() => {
    if (checkRulesId && checkRulesId?.length > 0) {
      checkRules(checkRulesId);
    }
  }, [checkRulesId]);

  return (
    <>
      <ProTable
        className={styles.rulesTable}
        headerTitle={title ?? '考核规则'}
        columns={ruleColumns({
          handEvent,
          isDelleteShow: !readonly,
        })}
        dataSource={dataSource}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <>
            {!readonly && (
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  setScoreRuleModalOpen({
                    open: true,
                    data: undefined,
                  });
                }}
                type="primary"
              >
                新增考核规则
              </Button>
            )}
          </>,
        ]}
      />
      <AddScoreRuleModal
        isReadonly={readonly}
        open={scoreRuleModalOpen.open}
        data={scoreRuleModalOpen.data}
        onSubmit={!!scoreRuleModalOpen.data ? onEditScheme : onAddScheme}
        onCancel={() => {
          setScoreRuleModalOpen({
            open: false,
            data: undefined,
          });
        }}
      />
    </>
  );
};
export default RulesTable;

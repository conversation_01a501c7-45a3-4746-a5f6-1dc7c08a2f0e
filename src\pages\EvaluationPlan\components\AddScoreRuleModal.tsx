import { getSchoolRoles, getSchoolUsers } from '@/services/utils';
import {
  ModalForm,
  ModalFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Space, Tag, Transfer } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

interface AddScoreRuleModalType extends ModalFormProps {
  onCancel: () => void;
  data?: API.IRule;
  onSubmit?: (value: any) => Promise<boolean | undefined>;
  isReadonly: boolean;
}

const AddScoreRuleModal: React.FC<AddScoreRuleModalType> = ({
  onCancel,
  data,
  onSubmit,
  isReadonly,
  ...restProps
}) => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const formRef = useRef<ProFormInstance>();
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [transferData, setTransferData] = useState<any[]>([]);

  const handleSubmit = async (values: any) => {
    try {
      if (onSubmit) {
        if (values?.assessedType === 'user' && !values?.users?.length) {
          return message.warning('参评人员不能为空');
        }

        const result = await onSubmit({
          ...values,
          rolesTypes: undefined,
          roles: values?.assessedType === 'role' ? values?.roles : undefined,
          users: values?.assessedType === 'user' ? values?.users : undefined,
        });

        // 提交成功后重置状态
        if (result) {
          setTargetKeys([]);
        }
      }
      return true;
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，' + error);
      return false;
    }
  };

  const handleChange = (newTargetKeys: React.Key[]) => {
    setTargetKeys(newTargetKeys as string[]);
    const selectedItems = transferData.filter((item) =>
      newTargetKeys.includes(item.key),
    );

    formRef.current?.setFieldsValue({
      users: selectedItems.map((item) => ({
        userCode: item.key,
        userName: item.title,
      })),
    });
  };

  /** 获取用户列表 */
  const fetchUserList = async () => {
    const { errCode, data, msg } = await getSchoolUsers({
      enterpriseCode: schoolInfo?.code || '',
    });
    if (errCode) {
      message.warning('获取用户列表失败，' + msg);
      setTransferData([]);
    } else {
      setTransferData(
        (data?.list ?? []).map((item: any) => ({
          title: item.name,
          description: item.code,
          key: item.code,
        })),
      );
    }
  };

  useEffect(() => {
    fetchUserList();
  }, []);

  useEffect(() => {
    if (data && restProps.open) {
      if (data?.assessedType === 'user') {
        const initialKeys = data.users?.map((user) => user.userCode) || [];
        setTargetKeys(initialKeys);
        formRef.current?.setFieldsValue({
          users: data.users || [],
        });
      }
      setTimeout(() => {
        const initialKeys = data.users?.map((user) => user.userCode) || [];
        setTargetKeys(initialKeys);
        formRef.current?.setFieldsValue({
          ...data,
          rolesTypes: data?.roles?.map((item: any) => item.roleCode) || [],
        });
      }, 0);
    }
  }, [data]);

  return (
    <>
      <ModalForm
        width={650}
        formRef={formRef}
        title={data ? '编辑考核规则' : '新增考核规则'}
        {...restProps}
        layout="horizontal"
        onFinish={handleSubmit}
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
          onCancel: () => {
            setTargetKeys([]);
            onCancel();
          },
          styles: {
            body: {
              marginTop: '20px',
              maxHeight: '500px',
              overflowY: 'auto',
              overflowX: 'hidden',
              paddingRight: '5px',
            },
          },
        }}
      >
        <ProFormText name="ruleId" hidden />
        <ProFormText name="roles" hidden />
        <ProFormText name="users" hidden />

        <ProFormText
          name="title"
          label="规则名称"
          rules={[{ required: true, message: '请输入规则名称' }]}
        />

        <ProFormRadio.Group
          readonly={isReadonly}
          name="assessedType"
          label="参评类型"
          initialValue="role"
          options={[
            { label: '身份', value: 'role' },
            { label: '个人', value: 'user' },
          ]}
          fieldProps={{
            onChange: (e) => {
              if (!data) {
                if (e.target.value === 'role') {
                  setTargetKeys([]);
                  formRef.current?.setFieldsValue({
                    users: undefined,
                  });
                } else {
                  formRef.current?.setFieldsValue({
                    roles: undefined,
                    rolesTypes: undefined,
                  });
                }
              }
            },
          }}
          rules={[{ required: true, message: '请选择参评类型' }]}
        />
        <ProFormDependency name={['assessedType']}>
          {({ assessedType }) => {
            if (!assessedType) return null;
            return assessedType === 'role' ? (
              <ProFormSelect
                name="rolesTypes"
                label="身份类型"
                mode="multiple"
                readonly={isReadonly}
                showSearch
                request={async () => {
                  const { errCode, data, msg } = await getSchoolRoles({
                    enterpriseCode: schoolInfo?.code || '',
                  });
                  if (errCode) {
                    message.warning('获取身份列表失败，' + msg);
                    return [];
                  }
                  return (data?.list ?? []).map((item: any) => ({
                    label: item.name,
                    value: item.code,
                  }));
                }}
                onChange={(_value, options) => {
                  const newData = options.map(
                    (item: { title: string; value: any }) => {
                      return {
                        roleName: item.title,
                        roleCode: item.value,
                      };
                    },
                  );
                  formRef.current?.setFieldsValue({
                    roles: newData,
                  });
                }}
                rules={[{ required: true, message: '请至少选择一个身份类型' }]}
              />
            ) : (
              <>
                {isReadonly ? (
                  <div className={styles.userListBox}>
                    <label className={styles.userListLabel}>已选参评人：</label>
                    <div className={styles.userList}>
                      {(data?.users ?? []).map((item: any, index: number) => {
                        return (
                          <Space key={index}>
                            <Tag>{item.userName}</Tag>
                          </Space>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <Transfer
                    dataSource={transferData}
                    targetKeys={targetKeys}
                    onChange={handleChange}
                    titles={['待选人员', '已选人员']}
                    render={(item) => `${item.title} (${item.description})`}
                    listStyle={{ width: '100%', height: 300 }}
                    showSearch
                  />
                )}
              </>
            );
          }}
        </ProFormDependency>
      </ModalForm>
    </>
  );
};
export default AddScoreRuleModal;

import ReportDetails from '@/components/ReportDetails';
import styles from './index.less';

const Detail: React.FC<{ data?: API.IScoreReport }> = ({ data }) => {
  if (!data) {
    return null;
  }

  return (
    <div className={styles.personalListDetail}>
      <div className={styles.detailWrapper}>
        <div className={styles.detailTitle}>
          {new Date().getFullYear()}年{data.month}月份月度考核
        </div>
        <ReportDetails info={data} />
      </div>
    </div>
  );
};

export default Detail;

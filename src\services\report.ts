import { request } from '@umijs/max';

/** 查询成绩报表 GET /score-report/list/{enterpriseCode}/{semester}/{month}/{ruleId} */
export async function getReports(
  enterpriseCode: string,
  semester: string,
  month: number,
  ruleId: number,
  query?: {
    assessedName?: string;
    isQuestion?: boolean;
  },
) {
  return request<API.ResType<{ list: API.IScoreReport[] }>>(
    `/score-report/list/${enterpriseCode}/${semester}/${month}/${ruleId}`,
    {
      method: 'GET',
      params: query,
    },
  );
}

/** 按考核分组查询成绩报告，仅获取问卷相关记录 GET /score-report/listForQuestion/{enterpriseCode}/{semester}/{month}/{ruleId} */
export async function getReports_question(
  enterpriseCode: string,
  semester: string,
  month: number,
  ruleId: number,
  query?: {
    assessedName?: string;
  },
) {
  return request<API.ResType<{ list: API.IScoreReportDetail[] }>>(
    `/score-report/listForQuestion/${enterpriseCode}/${semester}/${month}/${ruleId}`,
    {
      method: 'GET',
      params: query,
    },
  );
}

/** 按被考核人编号查询成绩报告 GET /score-report/one/{enterpriseCode}/{semester}/{assessedCode} */
export async function getReportsForAssessed(
  enterpriseCode: string,
  semester: string,
  assessedCode: string,
) {
  return request<API.ResType<{ list: API.IScoreReport[] }>>(
    `/score-report/one/${enterpriseCode}/${semester}/${assessedCode}`,
    {
      method: 'GET',
    },
  );
}

/** 按被考核人编号查询成绩报告 GET /score-report/detail/:detailId */
export async function editQuestionScore(detailId: number, score: number) {
  return request<API.ResType<{ list: API.IScoreReport[] }>>(
    `/score-report/detail/${detailId}`,
    {
      method: 'PATCH',
      data: {
        score,
      },
    },
  );
}

/** 导出成绩报表为Excel POST /score-report/export/:enterpriseCode/:semester/:month/:ruleId */
export async function exportToExcel(
  enterpriseCode: string,
  semester: string,
  month: number,
  ruleId?: number,
  query?: {
    assessedName?: string;
    isQuestion?: boolean;
  },
) {
  return request<API.ResType<{ buffer: Buffer }>>(
    `/score-report/export/${enterpriseCode}/${semester}/${month}/${ruleId}`,
    {
      method: 'GET',
      params: query,
    },
  );
}

/** 导出成填写进度 POST /score-report/exportProgress/:enterpriseCode/:semester/:month */
export async function exportProgress(
  enterpriseCode: string,
  semester: string,
  month: number,
) {
  return request<API.ResType<{ buffer: Buffer }>>(
    `/score-report/exportProgress/${enterpriseCode}/${semester}/${month}`,
    {
      method: 'GET',
    },
  );
}

import AutoLayout from '@/components/AutoLayout';
import NoData from '@/components/NoData';
import SemesterSelect from '@/components/SemesterSelect';
import { getReportsForAssessed } from '@/services/report';
import { envjudge } from '@/utils/calc';
import { DoubleRightOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Col, Drawer, Flex, message, Row } from 'antd';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import Detail from './Detail';
import styles from './index.less';

const PersonalList = () => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  const { initialState } = useModel('@@initialState');
  const { semesterData } = useModel('global');

  const [currentSemester, setCurrentSemester] = useState<string | undefined>(
    semesterData?.currentSemester,
  );
  const { currentUser, schoolInfo } = initialState || {};
  const [recordList, setRecordList] = useState<API.IScoreReport[]>([]);

  const [current, setCurrent] = useState<API.IScoreReport>();
  const [visible, setVisible] = useState(false);

  const getList = async (
    enterpriseCode: string,
    semester: string,
    assessedCode: string,
  ) => {
    const { errCode, data, msg } = await getReportsForAssessed(
      enterpriseCode,
      semester,
      assessedCode,
    );
    if (errCode) {
      message.error(msg || '查询失败');
      return;
    }
    setRecordList(data?.list || []);
  };
  useEffect(() => {
    if (!schoolInfo?.code || !currentUser?.userCode || !currentSemester) return;
    getList(schoolInfo.code, currentSemester, currentUser.userCode);
  }, [schoolInfo?.code, currentUser?.userCode, currentSemester]);

  return (
    <div className={styles.personalList}>
      <AutoLayout className={styles.searchWrapper}>
        <div style={{ marginBottom: isMobile ? '16px' : undefined }}>
          <SemesterSelect
            name="semester"
            label="学年学期"
            semesterChange={setCurrentSemester}
          />
        </div>
      </AutoLayout>
      <Row gutter={[24, 24]} wrap>
        {recordList?.length ? (
          recordList.map((item) => {
            return (
              <Col key={item.reportId} xl={8} md={12} sm={24} xs={24}>
                <div className={styles.cardBg}>
                  <div
                    className={classNames('card-wrapper', styles.peopleCard)}
                  >
                    <Flex
                      align="center"
                      justify="space-between"
                      style={{ width: '100%', height: '100%' }}
                    >
                      <h3>
                        {new Date().getFullYear()}年{item.month}月份月度考核
                      </h3>
                      <Button
                        color="primary"
                        variant="link"
                        onClick={() => {
                          setCurrent(item);
                          setVisible(true);
                        }}
                      >
                        查看详情
                        <DoubleRightOutlined />
                      </Button>
                    </Flex>
                    <Flex
                      align="center"
                      justify="center"
                      className={styles.scoreCon}
                    >
                      <span
                        className={
                          item.score ? styles.hasScore : styles.noScore
                        }
                        style={{
                          marginRight: '8px',
                        }}
                      >
                        {item.score ? (
                          <Flex align="baseline">
                            <span className={styles.score}>
                              {Number(item.score)}
                            </span>
                            <span>分</span>
                          </Flex>
                        ) : (
                          '无成绩'
                        )}
                      </span>
                    </Flex>
                  </div>
                </div>
              </Col>
            );
          })
        ) : (
          <NoData desc="暂无考核信息" />
        )}
      </Row>
      <Drawer
        title="考核详情"
        placement="right"
        width={isMobile ? '100%' : 900}
        open={visible}
        onClose={() => {
          setVisible(false);
        }}
      >
        <Detail data={current} />
      </Drawer>
    </div>
  );
};

export default PersonalList;

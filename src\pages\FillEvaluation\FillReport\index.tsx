import {
  assessmentTaskPoints,
  evaluateObservePoint,
} from '@/services/assessment_task';
import { controllerUploadReplace } from '@/services/utils';
import { envjudge } from '@/utils/calc';
import { ExclamationCircleFilled, LeftOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormList,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useLocation, useModel } from '@umijs/max';
import { Button, Col, Flex, Modal, Row, Upload, message } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';
// import { CosDeleteFile } from './util';

const formItemLayout = {
  labelCol: { flex: '7em' },
};
const FillReport = () => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  const formRef = useRef<ProFormInstance>();
  const { state: info }: any = useLocation();
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const [planInfo, setPlanInfo] = useState<any>({
    minAdjustment: 2,
    maxAdjustment: 2,
  });

  const getData = async () => {
    const { errCode, data, msg } = await assessmentTaskPoints({
      taskId: info?.taskId,
    });
    if (errCode) {
      message.warning('获取考核数据失败，' + msg);
    }
    let totalScore = 0;
    const convertData = (data?.list || []).map((item: any) => {
      const { files, scoreId, remark, observationPoint, pointId, scoreValue } =
        item;
      totalScore += Number(observationPoint?.baseScore);
      return {
        id: pointId,
        scoreId,
        name: observationPoint?.pointName,
        score: Number(observationPoint?.baseScore),
        desc: observationPoint?.description,
        realScore: scoreValue
          ? Number(scoreValue)
          : Number(observationPoint?.baseScore),
        remark,
        files: files?.map((file: string, index: number) => {
          return {
            name: '附件' + (index + 1),
            url: '//' + file,
          };
        }),
      };
    });
    setPlanInfo(data?.plan);
    formRef.current?.setFieldsValue({
      taskId: info?.taskId,
      assessor: info?.assessedName,
      ruleName: info?.ruleName,
      code: info?.assessedCode,
      totalScore: totalScore,
      scores: convertData,
    });
  };
  /** 从链接读取出key */
  // const getTencentCosKey = (url: string) => {
  //   // 创造正则表达式，匹配路径中的变量
  //   const regex = new RegExp(`/${TENCENT_COS.baseDir}(/.*)?`, 'i');
  //   const match = url.match(regex);
  //   return match ? match[0] : null;
  // };
  /** 上传资料 */
  // const customRequest = async (fileObj: any) => {
  //   const { file, onSuccess, onError } = fileObj;
  //   /** 重命名文件 */
  //   const rechristenName = `${schoolInfo?.code}_${dayjs().format(
  //     'YYYYMMDDHHmmss',
  //   )}`;
  //   const TENCENT_COS_KEY = TENCENT_COS.baseDir + `/` + rechristenName;
  //   const { error, data, status } = await CosUploadFile(TENCENT_COS_KEY, file);

  //   if (!status) {
  //     message.error(`上传失败! ${error}`);
  //     onError(error);
  //   } else {
  //     message.success('上传成功!');
  //     onSuccess(data?.Location, file);
  //   }
  // };

  /** 删除资料 */
  // const onRemove = (file: { response?: any }) => {
  //   return new Promise((resolve) => {
  //     Modal.confirm({
  //       title: '您确定要删除当前上传的文件吗？',
  //       icon: <ExclamationCircleFilled />,
  //       content: '此操作不可逆，请谨慎操作！',
  //       async onOk() {
  //         try {
  //           if (!file.response) {
  //             throw new Error('未找到文件信息');
  //           }

  //           const fileKey = getTencentCosKey(file.response);
  //           if (!fileKey) {
  //             throw new Error('未找到文件键');
  //           }

  //           const { error } = await CosDeleteFile(fileKey);
  //           if (error) {
  //             message.warning(`删除失败！${error}`);
  //             resolve(false);
  //           }

  //           message.success('文件删除成功！');
  //           resolve(true);
  //         } catch (err) {
  //           message.warning(`删除失败！${String(err)}`);
  //           resolve(false);
  //         }
  //       },
  //       onCancel() {
  //         // 用户取消操作
  //         resolve(false);
  //       },
  //     });
  //   });
  // };
  const onFinish = async (values: any) => {
    const { taskId, totalScore, scores } = values;
    let finalScore = 0;
    const convertData = scores.map((item: any) => {
      const { files, remark, scoreId, realScore } = item;
      finalScore += Number(realScore);
      return {
        scoreId,
        scoreValue: realScore ? Number(realScore) : realScore,
        remark: remark,
        files: files?.map((file: any) => file.response),
      };
    });
    if (
      totalScore - planInfo.minAdjustment <= finalScore &&
      finalScore <= totalScore + planInfo.maxAdjustment
    ) {
      const promises = convertData.map(async (data: any) => {
        const { scoreId, ...rest } = data;

        if (rest.files?.length) {
          rest.files = rest.files
            .map((file: any) => {
              return file?.data?.url || file?.url || file;
            })
            .filter(Boolean);
        }

        return await evaluateObservePoint(taskId, scoreId, { ...rest });
      });
      // 等待所有请求完成
      try {
        const resList: API.ResType<any>[] = await Promise.all(promises);
        if (resList.some((res) => res.errCode)) {
          message.warning(
            resList.find((res) => res.errCode)?.msg ||
              '提交失败！请联系管理员或稍后重试',
          );
          return;
        }
        message.success('提交成功！');
        history.back();
        // 如果需要返回数据，可以在这里处理
      } catch (error) {
        message.error('提交失败！请联系管理员或稍后重试');
      }
    } else {
      message.warning('请检查各项评分，当前实际得分超出基础分上下浮动范围！');
    }
  };
  useEffect(() => {
    if (info?.taskId) {
      getData();
    }
  }, [info]);
  return (
    <PageContainer
      ghost
      header={{
        title: ' ',
        breadcrumb: {},
        backIcon: (
          <>
            <LeftOutlined />
            返回上一页
          </>
        ),
        onBack: () => history.back(),
      }}
    >
      <div className={styles.fillReport}>
        <h1>考核评分表</h1>
        <div className={styles.itemWrapper}>
          <ProForm
            {...formItemLayout}
            formRef={formRef}
            layout={'horizontal'}
            grid
            submitter={{
              render: (props) => {
                return (
                  <Row className={styles.submitBar}>
                    <Col span={24}>
                      <Flex justify="center" align="center" gap={'24px'}>
                        <Button
                          color="primary"
                          variant="solid"
                          onClick={() => {
                            formRef.current
                              ?.validateFieldsReturnFormatValue?.()
                              .then(() => {
                                Modal.confirm({
                                  title: '您确定要提交考核评分表吗？',
                                  icon: <ExclamationCircleFilled />,
                                  content:
                                    '提交后考核评分表不可修改，请谨慎操作！',
                                  async onOk() {
                                    props?.submit?.();
                                  },
                                  onCancel() {},
                                });
                              });
                          }}
                        >
                          提交
                        </Button>
                        <Button
                          onClick={() => {
                            props?.reset?.();
                            history.back();
                          }}
                        >
                          取消
                        </Button>
                      </Flex>
                    </Col>
                  </Row>
                );
              },
            }}
            onFinish={onFinish}
            params={{}}
          >
            <ProFormText hidden name="taskId" />
            <ProFormText hidden name="totalScore" />
            <ProForm.Item>
              <div className={styles.titleBar}>基础信息</div>
            </ProForm.Item>
            <ProForm.Group
              className={classNames(
                styles.itemInfo,
                'card-wrapper',
                styles.greyBg,
              )}
            >
              <ProFormText
                name="assessor"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="被考核人"
              />
              <ProFormText
                name="code"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="工号"
              />
              <ProFormText
                name="ruleName"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="考核分组"
              />
            </ProForm.Group>
            <ProForm.Item>
              <div className={styles.titleBar}>
                <h3>观测点</h3>
                <p>
                  （ 基础分总计
                  {formRef.current?.getFieldValue('totalScore')}分
                  ，加分不得超过
                  {planInfo?.maxAdjustment}分，减分不得超过
                  {planInfo?.minAdjustment}分，需注明加减分的原因 ）
                </p>
              </div>
            </ProForm.Item>
            <ProFormList
              name="scores"
              creatorButtonProps={false}
              copyIconProps={false}
              deleteIconProps={false}
              alwaysShowItemLabel
            >
              {(_1, index, action) => {
                const item = action.getCurrentRowData() || {};
                return (
                  <div
                    className={classNames('card-wrapper', styles.borderedBg)}
                    key={item.id}
                  >
                    <ProFormText hidden name="scoreId" />
                    <div className={styles.itemTitle}>
                      {item.name}
                      <span>（基础分{item.score}分）</span>
                    </div>
                    <div>
                      <ProFormDigit
                        label="评分"
                        labelCol={{ flex: '8.5em' }}
                        colProps={{ span: 8 }}
                        name="realScore"
                        min={0}
                        max={item.score + planInfo.maxAdjustment}
                        fieldProps={{
                          precision: 1,
                          type: 'number', // 确保输入框类型为 number
                          step: 0.5, // 确保 step 属性设置为 0.5
                          onChange: (value) => {
                            // 动态控制 textarea 的必填状态
                            const isConditionMet = value === item.score;
                            formRef.current?.setFields([
                              {
                                name: ['scores', index, 'remark'], // 使用嵌套字段名
                                errors: isConditionMet ? [] : [''], // 清除错误信息
                              },
                              {
                                name: ['scores', index, 'files'], // 使用嵌套字段名
                                errors: isConditionMet ? [] : [''], // 清除错误信息
                              },
                            ]);
                          },
                        }}
                      />
                      <ProFormTextArea
                        labelCol={{ flex: '8.5em' }}
                        colProps={{ span: 24 }}
                        name="remark"
                        label="加减分原因"
                        tooltip={item.desc ? item.desc : undefined}
                        rules={[
                          ({ getFieldValue }: any) => ({
                            required:
                              getFieldValue(['scores', index, 'realScore']) !==
                              item.score,
                            message: '请输入加减分原因',
                          }),
                        ]}
                      />

                      <ProFormUploadButton
                        labelCol={{ flex: '8.5em' }}
                        colProps={{ span: 24 }}
                        label="上传资料"
                        name="files"
                        listType="text"
                        // rules={[
                        //   ({ getFieldValue }: any) => ({
                        //     required:
                        //       getFieldValue(['scores', index, 'realScore']) !==
                        //       item.score,
                        //     message: '请上传资料',
                        //   }),
                        // ]}
                        action={`/edu_api/upload/uploadSingle?subPath=fillReport/${schoolInfo?.code}`}
                        getValueFromEvent={(e: any) => {
                          if (Array.isArray(e)) {
                            return e;
                          }
                          return e?.fileList || [];
                        }}
                        max={3}
                        title="点击上传"
                        fieldProps={{
                          maxCount: 3,
                          onRemove: async (file) => {
                            try {
                              if (file?.response?.data?.id) {
                                const res = await controllerUploadReplace({
                                  id: file.response.data.id,
                                });
                                if (res?.errCode) {
                                  message.error(
                                    '删除失败，请联系管理员或稍后再试',
                                  );
                                  return false;
                                }
                                message.success('删除成功');
                                return true;
                              }
                              return true;
                            } catch (error) {
                              message.error('删除失败');
                              return false;
                            }
                          },
                          beforeUpload: (file) => {
                            if (file.size > 1024 * 1024 * 20) {
                              message.warning('文件大小不能超过20M');
                              return Upload.LIST_IGNORE;
                            }
                            return true;
                          },
                          itemRender: (originNode) => {
                            return (
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <span style={{ flex: 1 }}>资料</span>
                                {originNode.props.children[2]}
                              </div>
                            );
                          },
                        }}
                      />

                      {/* <ProFormUploadButton
                        labelCol={{ flex: '8.5em' }}
                        colProps={{ span: 24 }}
                        label="上传资料"
                        name="files"
                        listType="text"
                        action={`/edu_api/upload/uploadSingle/`}
                        rules={[
                          ({ getFieldValue }: any) => ({
                            required:
                              getFieldValue(['scores', index, 'realScore']) !==
                              item.score,
                            message: '请上传资料',
                          }),
                        ]}
                        fieldProps={{
                          multiple: true,
                          maxCount: 3,
                          onRemove: async (file) => {
                            const shouldRemove = await onRemove(file);
                            if (shouldRemove === false) {
                              // 如果取消删除，返回false阻止默认删除行为
                              return false;
                            }
                            return true;
                          },
                          // customRequest: customRequest,
                          beforeUpload: (file) => {
                            if (file.size > 1024 * 1024 * 20) {
                              message.warning('文件大小不能超过20M');
                              return Upload.LIST_IGNORE; // 表示忽略该文件，不加入列表
                            }
                            return true;
                          },
                          itemRender: (originNode) => {
                            return (
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <span style={{ flex: 1 }}>资料</span>
                                {originNode.props.children[2]}
                              </div>
                            );
                          },
                        }}
                      /> */}
                    </div>
                  </div>
                );
              }}
            </ProFormList>
          </ProForm>
        </div>
      </div>
    </PageContainer>
  );
};

export default FillReport;

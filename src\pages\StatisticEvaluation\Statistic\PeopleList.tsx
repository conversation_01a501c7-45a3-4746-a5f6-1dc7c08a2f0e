import { exportToExcel, getReports } from '@/services/report';
import { getQueryParams } from '@/utils/calc';
import { downloadByBuffer } from '@/utils/file';
import { LeftOutlined, SyncOutlined, UploadOutlined } from '@ant-design/icons';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useLocation, useModel } from '@umijs/max';
import { Button, Flex, Input, message, Space } from 'antd';
import { useState } from 'react';
import getColumns from './columns';
import styles from './index.less';
import ScoreInfo from './ScoreInfo';

const { Search } = Input;
const PeopleList = () => {
  const location = useLocation();
  const { state } = location;
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const { year, semester, month, ruleId } = getQueryParams(location.search);
  const { type } = (state as any) || { type: '教师' };
  const [filterText, setFilterText] = useState<string>('');

  /** 动态设置观测点列 */
  const [pontColumns, setPontColumns] = useState<API.IScoreReportDetail[]>([]);
  const [modalData, setModalData] = useState<{
    isModalOpen: boolean;
    detail?: API.IScoreReport;
  }>({
    isModalOpen: false,
  });
  const handleEvent = (type: string, data: API.IScoreReport) => {
    switch (type) {
      case 'detail':
        setModalData({
          isModalOpen: true,
          detail: data,
        });
        break;
      default:
        break;
    }
  };
  return (
    <PageContainer
      ghost
      header={{
        title: '返回上一页',
        breadcrumb: {},
        backIcon: (
          <>
            <LeftOutlined />
            返回上一页
          </>
        ),
        onBack: () => history.back(),
      }}
    >
      <header className={styles.fillingSituationHeader}>
        <div>
          {year} 年 {month} 月月度考核填报进度 - {schoolInfo?.name}
        </div>
      </header>
      <div className={styles.statisticRecordDetail}>
        <Flex align="center" justify="space-between">
          <Space
            align="center"
            size={[24, 24]}
            style={{
              marginBlock: 24,
            }}
          >
            <Search
              placeholder="搜索考核对象"
              onSearch={(value) => {
                setFilterText(value);
              }}
              style={{ width: 300 }}
            />
          </Space>
          <Space
            align="center"
            size={[24, 24]}
            style={{
              marginBlock: 24,
            }}
          >
            {type === '教师' && (
              <Button type="primary" icon={<SyncOutlined />}>
                家长问卷同步
              </Button>
            )}
            <Button
              icon={<UploadOutlined />}
              onClick={async () => {
                const enterpriseCode = schoolInfo?.code;
                if (!enterpriseCode || !semester || !month || !ruleId) {
                  message.error('导出参数错误，请联系管理员');
                  console.log('导出参数错误，请联系管理员');
                  console.log(enterpriseCode, semester, month, ruleId);
                  return;
                }
                const { errCode, msg, data } = await exportToExcel(
                  enterpriseCode,
                  semester,
                  Number(month),
                  Number(ruleId),
                );
                if (errCode) {
                  message.error(msg || '导出失败');
                  return;
                }
                downloadByBuffer(
                  data!.buffer,
                  `score-report-${enterpriseCode}-${semester}-${month}-${ruleId}.xlsx`,
                );
              }}
            >
              导出
            </Button>
          </Space>
        </Flex>
        <ProTable<
          API.IScoreReport,
          {
            enterpriseCode: string | null;
            semester: string | null;
            month: number | null;
            ruleId: number | null;
            assessedName?: string;
          }
        >
          bordered
          columns={getColumns({ pontColumns, handleEvent })}
          params={{
            enterpriseCode: schoolInfo?.code || null,
            semester,
            month: month ? Number(month) : null,
            ruleId: ruleId ? Number(ruleId) : null,
            assessedName: filterText,
          }}
          request={async (query) => {
            const { enterpriseCode, semester, month, ruleId, assessedName } =
              query;
            if (!enterpriseCode || !semester || !month || !ruleId) {
              setPontColumns([]);
              return {
                success: false,
                data: [],
              };
            }
            const { errCode, data, msg } = await getReports(
              enterpriseCode,
              semester,
              month,
              ruleId,
              { assessedName },
            );
            if (errCode) {
              message.error(msg);
              setPontColumns([]);
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
            setPontColumns(data?.list?.[0]?.scoreReportDetail || []);
            return {
              data: data?.list || [],
              total: data?.list.length || 0,
              success: true,
            };
            // return {
            //   data: [{ id: 1, name: '张三' }],
            //   total: 0,
            //   success: true,
            // };
          }}
          rowKey="id"
          options={false}
          search={false}
          pagination={{
            pageSize: 10,
          }}
        />
      </div>
      <ScoreInfo
        modalData={modalData}
        handleCancel={() => {
          setModalData({
            isModalOpen: false,
          });
        }}
      />
    </PageContainer>
  );
};

export default PeopleList;

.fillReport {
  h1 {
    text-align: center;
    color: #333;
    font-family: 'PingFang SC';
    font-size: 28px;
    font-weight: normal;
    letter-spacing: 1.28px;
  }

  .itemWrapper {
    .titleBar {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      color: #000;
      font-family: 'PingFang SC';
      font-size: 18px;
      font-style: normal;
      line-height: 50px;
      letter-spacing: 1px;

      h3 {
        margin: 0;
      }

      &::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 22px;
        border-radius: 2px;
        margin-right: 8px;
        background: var(--primary-color);
      }

      p {
        margin: 0;
        color: #6b7280;
        font-size: 12px;
        line-height: 26px;
      }
    }

    :global {
      .ant-form-item {
        label {
          color: rgba(107, 114, 128, 100%);
        }
      }

      .ant-pro-form-group {
        background: #f9fafb;
        border-radius: var(--border-radius-base);
        box-shadow: var(--card-shadow);
      }

      .ant-pro-form-list {
        .ant-pro-form-list-item.ant-pro-form-list-item-show-label {
          width: 100%;
        }

        .ant-upload {
          margin-bottom: 8px;
        }

        .ant-upload-list {
          display: flex;
          flex-wrap: wrap;

          .ant-upload-list-item {
            margin: 0;
          }
        }
      }
    }

    .borderedBg {
      border: 1px solid #dadada;
      margin-bottom: 24px;
      padding: 16px;

      .itemTitle {
        font-weight: bold;
        font-size: 16px;

        span {
          color: rgba(107, 114, 128, 100%);
          font-size: 14px;
          font-weight: normal;
        }
      }
    }

    .submitBar {
      button {
        width: 120px;
        height: 36px;
        border-radius: var(--border-radius-base);
      }
    }
  }
}

// 全局共享数据示例
import { getAllSemesterList } from '@/services/utils';
import { getCurrentXQ } from '@/utils/calc';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
// 定义类型以提高代码可读性和安全性
interface SemesterItem {
  semester_code: string;
  semester_name: string;
}

interface SemesterData {
  semesterList?: { label: string; value: string }[];
  currentSemester?: string;
}
const GetSemesterData = (): {
  semesterData?: SemesterData;
  refresh: () => Promise<SemesterData>;
} => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const [semesterData, setSemesterData] = useState<SemesterData>();
  const refresh = useCallback(async () => {
    if (!initialState?.schoolInfo) {
      setSemesterData({});
      return {};
    }
    const { errCode, data, msg } = await getAllSemesterList({
      enterpriseCode: schoolInfo?.code,
    });
    if (errCode) {
      message.warning('获取学年学期失败，' + msg);
      setSemesterData({});
      return {};
    }
    const { list } = data || {};
    if (!Array.isArray(list) || list.length === 0) {
      return {};
    }

    const curTerm = getCurrentXQ(list);
    if (!curTerm) {
      message.warning('无法确定当前学期');
      return {};
    }

    const semesterList = list.map((item: SemesterItem) => ({
      label: `${item.semester_code.substring(
        0,
        4,
      )}-${item.semester_code.substring(4, 8)} ${item.semester_name}`,
      value: item.semester_code,
    }));

    const newData: SemesterData = {
      semesterList,
      currentSemester: curTerm.semester_code,
    };
    setSemesterData(newData);
    return newData;
  }, [schoolInfo]);
  useEffect(() => {
    if (schoolInfo?.code) {
      refresh();
    }
  }, [schoolInfo]);

  return {
    semesterData,
    refresh,
  };
};

export default GetSemesterData;

import { assessmentTaskProgress } from '@/services/assessment_task';
import { getQueryParams } from '@/utils/calc';
import { LeftOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Tabs, Tag } from 'antd';
import React from 'react';
import QuestionScoreManager from '../QuestionScoreManager';
import styles from './index.less';

const FillingSituation: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const { month, semester, year, ruleId, ruleName } = getQueryParams();

  const columns: ProColumns[] = [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 48,
    },
    {
      title: '姓名',
      align: 'center',
      dataIndex: 'assessorName',
      search: true,
    },
    {
      title: '考核分组',
      dataIndex: 'ruleName',
      search: false,
      align: 'center',
    },
    {
      title: '总数',
      align: 'center',
      dataIndex: 'total',
      search: false,
    },
    {
      title: '已填写项',
      align: 'center',
      dataIndex: 'completedCount',
      search: false,
    },
    {
      title: '未填写项',
      align: 'center',
      dataIndex: 'notCompletedCount',
      search: false,
    },
    {
      title: '填写状态',
      dataIndex: 'state',
      filters: true,
      valueType: 'select',
      align: 'center',
      search: false,
      valueEnum: {
        completed: { text: '已完成' },
        uncompleted: { text: '未完成' },
      },
      onFilter: (value, record) => {
        if (value === 'completed') {
          return record.completedCount === String(record.total);
        } else if (value === 'uncompleted') {
          return record.completedCount !== String(record.total);
        }
        return true;
      },
      render: (_dom, record) => {
        return record.completedCount === String(record.total) ? (
          <Tag color="#87d068">已完成</Tag>
        ) : (
          <Tag color="#FFB833">未完成</Tag>
        );
      },
    },
  ];

  const dom1 = (
    <ProTable<API.IAssessmentTask>
      className={styles.fillingSituationTable}
      columns={columns}
      params={{ ruleName }}
      request={async (params) => {
        const { assessorName, ruleName } = params;
        const { errCode, data, msg } = await assessmentTaskProgress({
          enterpriseCode: schoolInfo?.code || '',
          semester: semester || '',
          month: month || '',
          assessorName: assessorName || undefined,
          ruleName: ruleName || undefined,
        });
        if (errCode) {
          message.warning('获取考核规则列表失败，' + msg);
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
        return {
          data: data?.list,
          total: data?.total,
          success: true,
        };
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        defaultValue: {
          option: { fixed: 'right', disable: true },
        },
      }}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      options={false}
      form={{
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 10,
      }}
      dateFormatter="string"
    />
  );

  return (
    <>
      <PageContainer
        ghost
        header={{
          title: '返回上一页',
          breadcrumb: {},
          backIcon: (
            <>
              <LeftOutlined />
              返回上一页
            </>
          ),
          onBack: () => history.back(),
        }}
      >
        <header className={styles.fillingSituationHeader}>
          {year} 年 {month} 月月度考核填报进度 - {schoolInfo?.name}
        </header>
        <Tabs
          items={[
            {
              label: '填写进度查看',
              key: 'fillingSituation',
              children: dom1,
            },
            {
              label: '问卷成绩管理',
              key: 'questionnaire',
              children: (
                <QuestionScoreManager
                  enterpriseCode={schoolInfo?.code || ''}
                  semester={semester || ''}
                  month={(month || 0) as number}
                  ruleId={(ruleId || 0) as number}
                />
              ),
            },
          ]}
        />
      </PageContainer>
    </>
  );
};
export default FillingSituation;

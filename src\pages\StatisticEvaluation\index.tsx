import SemesterSelect from '@/components/SemesterSelect';
import { assessmentTaskIndex } from '@/services/assessment_task';
import {
  getPublicityListAPI,
  publishPublicityAPI,
  revokePublicityAPI,
} from '@/services/publication';
import { exportProgress, exportToExcel } from '@/services/report';
import { downloadByBuffer } from '@/utils/file';
import {
  ModalForm,
  PageContainer,
  ProFormDateRangePicker,
  ProList,
} from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { Button, Popconfirm, Progress, Tag, message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

const exportHandler = async (
  isPublic: boolean,
  enterpriseCode: string,
  semester: string,
  month: number,
) => {
  if (!enterpriseCode || !semester || !month) {
    message.error('导出参数错误，请联系管理员');
    console.log('导出参数错误，请联系管理员');
    console.log(enterpriseCode, semester, month);
    return;
  }
  const method = isPublic ? exportToExcel : exportProgress;
  const { errCode, msg, data } = await method(
    enterpriseCode,
    semester,
    Number(month),
  );
  if (errCode) {
    message.error(msg || '导出失败');
    return;
  }
  downloadByBuffer(
    data!.buffer,
    `score-report-${enterpriseCode}-${semester}-${month}.xlsx`,
  );
};

const StatisticEvaluation = () => {
  const { semesterData } = useModel('global');
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const [currentSemester, setCurrentSemester] = useState(
    semesterData?.currentSemester ?? '',
  );
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [retryingTasks, setRetryingTasks] = useState<{
    [key: string]: boolean;
  }>({});
  const [open, setOpen] = useState<{
    isModalOpen: boolean;
    data?: any;
  }>({
    isModalOpen: false,
    data: undefined,
  });

  const fetchInitialData = async () => {
    if (currentSemester && schoolInfo?.code) {
      setLoading(true);
      try {
        const { errCode, data, msg } = await assessmentTaskIndex({
          enterpriseCode: schoolInfo?.code,
          semester: currentSemester,
        });

        if (errCode) {
          message.warning('获取考核列表数据失败，' + msg);
          return;
        }

        const initialTasks: (API.ITaskMonthItme & {
          isPublic: boolean;
          status: string;
        })[] =
          data?.list?.map((task) => ({
            ...task,
            isPublic: false,
            status: '待查询',
          })) || [];
        setDataSource(initialTasks);
        //  并行查询公示状态
        const publicityPromises = initialTasks.map((task: { month: any }) =>
          getPublicityListAPI(schoolInfo?.code || '', {
            month: task.month,
            semester: currentSemester,
          }),
        );
        const results = await Promise.allSettled(publicityPromises);
        setDataSource((prev) =>
          prev.map((item, index) => {
            const result = results[index];
            if (result.status === 'fulfilled') {
              const { errCode, data: publicityData } = result.value;
              return {
                ...item,
                isPublic: !errCode && !!publicityData?.list?.length,
                status: !errCode
                  ? !!publicityData?.list?.length
                    ? '已公示'
                    : '未公示'
                  : '查询失败',
              };
            }
            return { ...item, status: '查询失败' };
          }),
        );
      } catch (error) {
        message.error('数据加载失败');
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, [currentSemester, schoolInfo?.code]);

  const retryPublicStatus = async (task: any) => {
    setRetryingTasks((prev) => ({
      ...prev,
      [`${task.year}-${task.month}`]: true,
    }));
    try {
      const { errCode, data: publicityData } = await getPublicityListAPI(
        schoolInfo?.code || '',
        { month: task.month, semester: currentSemester },
      );
      setDataSource((prev) =>
        prev.map((item) =>
          item.month === task.month
            ? {
                ...item,
                isPublic: !errCode && (publicityData?.list ?? []).length > 0,
                status: !errCode
                  ? (publicityData?.list ?? [])?.length > 0
                    ? '已公示'
                    : '未公示'
                  : '查询失败',
              }
            : item,
        ),
      );
    } catch (error) {
      message.warning('查询公示状态失败');
    } finally {
      setRetryingTasks((prev) => ({
        ...prev,
        [`${task.year}-${task.month}`]: false,
      }));
    }
  };

  const isPublicStatus = (status: string, row: any) => {
    switch (status) {
      case '已公示':
        return <Tag color="success">已公示</Tag>;
      case '未公示':
        return <Tag color="default">未公示</Tag>;
      case '查询失败':
        return (
          <Button
            type="link"
            size="small"
            loading={retryingTasks[`${row.year}-${row.month}`]}
            onClick={() => retryPublicStatus(row)}
          >
            重试
          </Button>
        );
      case '待查询':
        return <Tag color="processing">查询中...</Tag>;
      default:
        return '-';
    }
  };

  /** 开始公示 */
  const startPublicity = async (values: any) => {
    setOpen({
      isModalOpen: true,
      data: values,
    });
  };

  /** 停止公示 */
  const stopPublicity = async (value: any) => {
    const { errCode, msg } = await revokePublicityAPI({
      enterpriseCode: schoolInfo?.code || '',
      month: value?.month,
      semester: currentSemester,
    });
    if (errCode) {
      message.warning('停止公示失败，' + msg);
    } else {
      message.success('停止公示成功');
      fetchInitialData();
    }
  };

  return (
    <PageContainer
      ghost
      header={{
        title: false,
        breadcrumb: {},
      }}
    >
      <ProList<API.ITaskMonthItme & { isPublic: boolean; status: string }>
        dataSource={dataSource}
        loading={loading}
        search={false}
        rowKey="name"
        headerTitle={
          <SemesterSelect
            name="semester"
            label="学年学期"
            semesterChange={setCurrentSemester}
          />
        }
        pagination={false}
        metas={{
          title: {
            dataIndex: 'month',
            title: 'month',
            render: (_, row) => {
              return (
                <div>
                  {row.year}年{row.month}月月度考核填报
                </div>
              );
            },
          },
          description: {
            dataIndex: 'fillableDates',
            render: (_, row) => {
              const { fillableDates } = row;
              return (
                <div>
                  填报日期：
                  <span>{dayjs(fillableDates?.[0]).format('MM月DD日')}</span>
                  {fillableDates?.length > 1 && (
                    <span>
                      &nbsp;&nbsp;至&nbsp;&nbsp;
                      {dayjs(fillableDates?.[fillableDates?.length - 1]).format(
                        'MM月DD日',
                      )}
                    </span>
                  )}
                </div>
              );
            },
          },
          content: {
            render: (_, row) => {
              const { completedCount, notCompletedCount, total } = row;
              const rate = (
                (Number(completedCount) / Number(total)) *
                100
              ).toFixed(2);
              return (
                <div
                  style={{ display: 'flex', justifyContent: 'space-around' }}
                >
                  <div
                    style={{
                      width: '60px',
                      textAlign: 'center',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <div>已填写项</div>
                    <Tag bordered={false} color="success">
                      {completedCount}
                    </Tag>
                  </div>
                  <div
                    style={{
                      width: '60px',
                      textAlign: 'center',
                    }}
                  >
                    <div>待填写项</div>
                    <Tag bordered={false} color="processing">
                      {notCompletedCount}
                    </Tag>
                  </div>
                  <div
                    style={{
                      width: '200px',
                    }}
                  >
                    <div>填写进度</div>
                    <Progress percent={Number(rate)} />
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      width: '80px',
                      textAlign: 'center',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <>{isPublicStatus(row.status, row)}</>
                  </div>
                </div>
              );
            },
          },
          actions: {
            render: (text, row) => {
              return (
                <>
                  {
                    <>
                      {row.total && (
                        <>
                          <Button
                            type="link"
                            onClick={() => {
                              history.push(
                                `/statisticEvaluation/statistic?month=${row.month}&year=${row.year}&semester=${row.semester}&status=${row.status}`,
                              );
                            }}
                            key="list-check"
                          >
                            查看
                          </Button>

                          {row?.status !== '待查询' && (
                            <>
                              <Button
                                type="link"
                                onClick={() => {
                                  exportHandler(
                                    !!row.isPublic,
                                    row.enterpriseCode,
                                    row.semester,
                                    row.month,
                                  );
                                }}
                              >
                                导出
                              </Button>
                              {row.isPublic ? (
                                <Popconfirm
                                  title="确定要撤消公示吗？"
                                  onConfirm={() => stopPublicity(row)}
                                  okText="确定"
                                  cancelText="取消"
                                >
                                  <Button type="link" danger>
                                    撤消公示
                                  </Button>
                                </Popconfirm>
                              ) : (
                                <Button
                                  type="link"
                                  onClick={() => startPublicity(row)}
                                >
                                  开始公示
                                </Button>
                              )}
                            </>
                          )}
                        </>
                      )}
                    </>
                  }
                </>
              );
            },
          },
        }}
      />
      <ModalForm
        width={500}
        title="开始公示"
        open={open.isModalOpen}
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          onCancel() {
            setOpen({
              isModalOpen: false,
              data: undefined,
            });
          },
          styles: {
            body: {
              marginTop: '20px',
            },
          },
        }}
        onFinish={async (value) => {
          const { errCode, msg } = await publishPublicityAPI(
            schoolInfo?.code || '',
            open.data?.month || '',
            currentSemester,
            {
              startTime: value.startTime,
              endTime: value.endTime,
            },
          );
          if (errCode) {
            message.warning('公示失败，' + msg);
          } else {
            message.success('公示成功');
          }
          setOpen({
            isModalOpen: false,
            data: undefined,
          });
          fetchInitialData();
        }}
      >
        <ProFormDateRangePicker
          name="公示起止时间"
          label="公示起止时间"
          rules={[{ required: true, message: '公示起止时间是必填项' }]}
          fieldProps={{
            disabledDate: (current) => {
              return current && current < dayjs().startOf('day');
            },
          }}
          transform={(value) => {
            return {
              startTime: value[0],
              endTime: value[1],
            };
          }}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default StatisticEvaluation;

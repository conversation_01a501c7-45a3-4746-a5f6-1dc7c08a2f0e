import NoData from '@/components/NoData';
import SemesterSelect from '@/components/SemesterSelect';
import { assessmentTaskIndex } from '@/services/assessment_task';
import { PageContainer, ProList } from '@ant-design/pro-components';
import { Link, useModel } from '@umijs/max';
import { Progress, Tag, message } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import './index.less';

const FillEvaluation = () => {
  const { semesterData } = useModel('global');
  const { initialState } = useModel('@@initialState');
  const { currentUser, schoolInfo } = initialState || {};
  const [currentSemester, setCurrentSemester] = useState(
    semesterData?.currentSemester ?? '',
  );
  return (
    <PageContainer
      className={'fillContainer'}
      ghost
      header={{
        title: false,
        breadcrumb: {},
      }}
    >
      <ProList<API.ITaskMonthItme>
        search={false}
        headerTitle={
          <SemesterSelect
            name="semester"
            label="学年学期"
            semesterChange={setCurrentSemester}
          />
        }
        params={{
          currentSemester,
        }}
        request={async () => {
          if (currentSemester && schoolInfo?.code) {
            const { errCode, data, msg } = await assessmentTaskIndex({
              assessorCode: currentUser?.userCode,
              enterpriseCode: schoolInfo?.code,
              semester: currentSemester,
            });
            if (errCode) {
              message.warning('获取考核列表数据失败，' + msg);
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
            return {
              data: data?.list || [],
              total: data?.total || 0,
              success: true,
            };
          } else {
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        pagination={false}
        metas={{
          title: {
            dataIndex: 'month',
            title: 'month',
            render: (_, row) => {
              return (
                <div>
                  {row.year}年{row.month}月月度考核填报
                </div>
              );
            },
          },
          description: {
            dataIndex: 'fillableDates',
            render: (_, row) => {
              const { fillableDates } = row;
              return (
                <div>
                  填报日期：
                  {!fillableDates?.length ? (
                    <>整月</>
                  ) : (
                    <>
                      <span>
                        {dayjs(fillableDates?.[0]).format('MM月DD日')}
                      </span>
                      {fillableDates?.length > 1 && (
                        <span>
                          &nbsp;&nbsp;至&nbsp;&nbsp;
                          {dayjs(
                            fillableDates?.[fillableDates?.length - 1],
                          ).format('MM月DD日')}
                        </span>
                      )}
                    </>
                  )}
                </div>
              );
            },
          },
          content: {
            render: (_, row) => {
              const { completedCount, notCompletedCount, total } = row;
              const rate = (
                (Number(completedCount) / Number(total)) *
                100
              ).toFixed(2);
              return (
                <div
                  style={{ display: 'flex', justifyContent: 'space-around' }}
                >
                  <div
                    style={{
                      width: '60px',
                      textAlign: 'center',
                    }}
                  >
                    <div>已填写项</div>
                    <Tag bordered={false} color="success">
                      {completedCount}
                    </Tag>
                  </div>
                  <div
                    style={{
                      width: '60px',
                      textAlign: 'center',
                    }}
                  >
                    <div>待填写项</div>
                    <Tag bordered={false} color="processing">
                      {notCompletedCount}
                    </Tag>
                  </div>
                  <div
                    style={{
                      width: '200px',
                    }}
                  >
                    <div>填写进度</div>
                    <Progress percent={Number(rate)} />
                  </div>
                </div>
              );
            },
          },
          actions: {
            render: (_text, row) => {
              if (row.total)
                return [
                  <Link
                    key="list-check"
                    to={'/fillEvaluation/assessor'}
                    rel="noopener noreferrer"
                    state={row}
                  >
                    {(row.fillableDates || [])
                      .map((t) => t.split('T')[0])
                      .includes(dayjs().format('YYYY-MM-DD'))
                      ? '填写'
                      : '查看'}
                  </Link>,
                ];
            },
          },
        }}
        locale={{
          emptyText: <NoData desc="您没有需要进行填报的内容" />,
        }}
      />
    </PageContainer>
  );
};

export default FillEvaluation;

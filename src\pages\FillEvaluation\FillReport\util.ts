import cos, { opt } from '@/constants/cos';
/**腾讯 cos 删除 */
export const CosDeleteFile = async (
  fileKey: string,
): Promise<{
  status: boolean;
  error?: unknown;
  data?: any;
}> => {
  try {
    const data = await new Promise((resolve, reject) => {
      cos.deleteObject(
        {
          Bucket: opt.bucket,
          Region: opt.region,
          Key: fileKey,
        },
        (err, data) => {
          if (err) {
            return reject(err);
          }
          resolve(data);
        },
      );
    });
    return { status: true, data };
  } catch (error) {
    return { status: false, error };
  }
};

/** 腾讯 cos 上传 */
export const CosUploadFile = async (
  /** 上传文件的key */
  FileKey: string,
  /** 需要上传的文件 */
  fileBody: any,
): Promise<{
  status: boolean;
  error?: unknown;
  data?: any;
}> => {
  try {
    const data = await new Promise((resolve, reject) => {
      cos.uploadFile({
        Bucket: opt.bucket,
        Region: opt.region,
        Key: FileKey,
        Body: fileBody,
        SliceSize: 1024 * 1024 * 5,
        onFileFinish: (err, data) => {
          if (err) {
            return reject(err);
          }
          resolve(data);
        },
      });
    });
    return { status: true, data };
  } catch (error) {
    return { status: false, error };
  }
};

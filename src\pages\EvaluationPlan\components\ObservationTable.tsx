import {
  createEvaluationObservationPointAPI,
  getAllEvaluationObservationPointAPI,
  removeEvaluationObservationPointAPI,
} from '@/services/evaluation_observation';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProFormDigit,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Col, message, Row } from 'antd';
import React, { useRef, useState } from 'react';
import AddObservationModal from './AddObservationModal';
import { observationColumns } from './columns';

interface ObservationTableProps {
  scoringId: number | undefined;
  title: string;
  readonly: boolean;
}
const ObservationTable: React.FC<ObservationTableProps> = ({
  scoringId,
  title,
  readonly,
}) => {
  const actionRef = useRef<ActionType>();
  const [observationModalOpen, setObservationModalOpen] = useState({
    open: false,
    data: undefined,
  });
  const [isModalOpen, setIsModalOpen] = useState<{
    open: boolean;
    data?: API.IObservationPoint;
  }>({
    open: false,
    data: undefined,
  });
  const [dataList, setDataList] = useState<API.IObservationPoint[]>([]);

  /** 新增 */
  const onAddObservation = async (
    formData: Record<string, any>,
  ): Promise<boolean> => {
    const values = formData as { list: Partial<API.IObservationPoint>[] };
    if (!scoringId) {
      return message.warning('新增失败，scoringId 不能为空');
    }
    if (!values) {
      return message.warning('新增失败，新增内容不能为空');
    }
    if (dataList.length !== 0) {
      const newData = dataList.map((item) => {
        return {
          baseScore: item.baseScore,
          description: item.description || '',
          pointName: item.pointName,
        };
      });
      values.list = values.list.concat(newData);
    }

    const { errCode, msg } = await createEvaluationObservationPointAPI(
      scoringId,
      values,
    );
    if (errCode) {
      message.warning('新增观测点失败，' + msg);
      return false;
    }
    message.success('新增观测点成功');
    actionRef.current?.reload();
    setObservationModalOpen({ open: false, data: undefined });
    return true;
  };

  /** 删除 */
  const onDelete = async (values: API.IObservationPoint) => {
    if (!scoringId) return message.warning('删除失败，planId 不存在');
    if (!values?.pointId) return message.warning('删除失败，pointId 不存在');

    const { errCode, msg } = await removeEvaluationObservationPointAPI(
      scoringId,
      values.pointId,
    );
    if (errCode) {
      message.warning('删除观测点失败，' + msg);
    }
    message.success('删除观测点成功');
    actionRef.current?.reload();
  };

  /** 编辑 */
  const onEdit = async (values: API.IObservationPoint) => {
    const findData = dataList.find(
      (item) => item.pointId === isModalOpen.data?.pointId,
    );
    if (findData) {
      const isDuplicate = dataList.some(
        (item) =>
          item.pointId !== findData.pointId &&
          item.pointName === values.pointName,
      );

      if (isDuplicate) {
        message.warning('修改失败，观测点名称重复');
        return false;
      }

      findData.baseScore = values.baseScore;
      findData.description = values.description || '';
      findData.pointName = values.pointName;
      if (!scoringId) {
        return message.warning('新增失败，scoringId 不能为空');
      }
      if (!values) {
        return message.warning('新增失败，新增内容不能为空');
      }
      const { errCode, msg } = await createEvaluationObservationPointAPI(
        scoringId,
        {
          list: dataList.map((item) => ({
            baseScore: item.baseScore,
            description: item.description || '',
            pointName: item.pointName,
          })),
        },
      );
      if (errCode) {
        message.warning('修改观测点失败，' + msg);
        return false;
      }
      message.success('修改观测点成功');
      actionRef.current?.reload();
      setIsModalOpen({ open: false, data: undefined });
      return true;
    } else {
      message.warning('修改观测点失败，未找到对应数据！');
      return false;
    }
  };

  const handEvent = (type: string, data: API.IObservationPoint) => {
    switch (type) {
      case 'edit':
        setIsModalOpen({ open: true, data });
        break;
      case 'remove':
        onDelete(data);
        break;
    }
  };

  return (
    <>
      <ProTable
        headerTitle={title ?? '观测点'}
        columns={observationColumns({ handEvent, isDelleteShow: !readonly })}
        actionRef={actionRef}
        request={async () => {
          if (!scoringId) {
            message.warning('未读取到考核方案信息，请稍后重试');
          }
          const { errCode, msg, data } =
            await getAllEvaluationObservationPointAPI(scoringId);
          if (errCode) {
            message.warning('获取观测点列表失败，' + msg);
            return {
              data: [],
              success: false,
            };
          }
          setDataList(data?.list || []);
          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <>
            {!readonly && (
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  setObservationModalOpen({
                    open: true,
                    data: undefined,
                  });
                }}
                type="primary"
              >
                批量新增观测点
              </Button>
            )}
          </>,
        ]}
      />
      <AddObservationModal
        open={observationModalOpen.open}
        data={observationModalOpen.data}
        onFinish={onAddObservation}
        onCancel={() => {
          setObservationModalOpen({
            open: false,
            data: undefined,
          });
        }}
      />
      <ModalForm
        title="编辑观测点"
        width={500}
        open={isModalOpen.open}
        layout="horizontal"
        initialValues={{ ...isModalOpen.data }}
        onFinish={onEdit}
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
          onCancel: () => {
            setIsModalOpen({ open: false, data: undefined });
          },
          styles: {
            body: {
              marginTop: '20px',
            },
          },
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <ProFormText
              name="pointName"
              label="名称"
              rules={[{ required: true, message: '请输入观测点名称' }]}
              placeholder="请输入观测点名称"
            />
          </Col>
          <Col span={12}>
            <ProFormDigit
              name="baseScore"
              label="分值"
              min={0}
              max={100}
              fieldProps={{ precision: 0, suffix: '分' }}
              rules={[{ required: true, message: '请输入0-100的整数分值' }]}
            />
          </Col>
        </Row>

        <ProFormText
          labelCol={{
            flex: '3.7em',
          }}
          name="description"
          label="说明"
          placeholder="请输入打分说明"
          fieldProps={{ maxLength: 200 }}
        />
      </ModalForm>
    </>
  );
};
export default ObservationTable;

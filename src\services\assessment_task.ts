import { request } from '@umijs/max';
/** 按学期获取考核任务月度汇总列表  GET /assessment-task/month/list/{enterpriseCode}/{semester} */
export async function assessmentTaskIndex(params: Record<string, any>) {
  const { enterpriseCode, semester, ...rest } = params;
  return request<API.ResType<{ list: API.ITaskMonthItme[]; total?: number }>>(
    `/assessment-task/month/list/${enterpriseCode}/${semester}`,
    {
      method: 'GET',
      params: {
        ...rest,
      },
    },
  );
}
/** 获取月度考核任务  GET /assessment-task/monthly/{enterpriseCode}/{semester}/{month} */
export async function assessmentTaskMonth(params: {
  enterpriseCode?: string;
  semester: string;
  month: number;
  [key: string]: any;
}) {
  const { enterpriseCode, semester, month, ...rest } = params;
  return request<
    API.ResType<{ list: API.IAssessmentTask[]; pubPlans?: { planId: number } }>
  >(`/assessment-task/monthly/${enterpriseCode}/${semester}/${month}`, {
    method: 'GET',
    params: {
      ...rest,
    },
  });
}
/** 根据任务ID获取评分项  GET /score/tasks/{taskId} */
export async function assessmentTaskPoints(params: Record<string, any>) {
  const { taskId, ...rest } = params;
  return request<any>(`/score/tasks/${taskId}`, {
    method: 'GET',
    params: {
      ...rest,
    },
  });
}
/** 提交评分  PATCH /score/{taskId}/{scoreId} */
export async function evaluateObservePoint(
  taskId: string,
  scoreId: string,
  body: any,
) {
  return request<API.ResType>(`/score/${taskId}/${scoreId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 获取个人任务填写进度 GET /assessment-task/monthly/{enterpriseCode}/{semester}/{month}/progress */
export async function assessmentTaskProgress(params: {
  enterpriseCode: string;
  semester: string;
  month: string;
  ruleName?: string;
  assessorName?: string;
}) {
  const { enterpriseCode, semester, month, assessorName, ruleName } = params;
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/assessment-task/monthlyProgress/${enterpriseCode}/${semester}/${month}`,
    {
      method: 'GET',
      params: {
        assessorName,
        ruleName,
      },
    },
  );
}

/** 查月任填进度 GET /publication/actived/{enterpriseCode} */
export async function getPublicationList(params: {
  enterpriseCode: string;
  semester?: string;
  month?: string;
}) {
  const { enterpriseCode, month, semester } = params;
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/publication/actived/${enterpriseCode}`,
    {
      method: 'GET',
      params: {
        semester,
        month,
      },
    },
  );
}

/** 获取考核分组 GET /assessment-task/groups/{enterpriseCode}/{semester}/{month} */
export async function getAssessmentTaskGroups(params: {
  enterpriseCode: string;
  semester: string;
  month: number;
}) {
  const { enterpriseCode, semester, month } = params;
  return request<
    API.ResType<{
      total?: number;
      list?: { ruleId: number; ruleName: string }[];
    }>
  >(`/assessment-task/groups/${enterpriseCode}/${semester}/${month}`, {
    method: 'GET',
  });
}

/**  GET 按考核规则获取整体任务填写进度 /assessment-task/analysis/group/{enterpriseCode}/{semester}/{month}/{ruleId} */
export async function getAssessmentTaskAnalysis(params: {
  enterpriseCode: string;
  semester: string;
  month: string;
  ruleId: number;
}) {
  const { enterpriseCode, semester, month, ruleId } = params;
  return request<
    API.ResType<{
      total: number;
      pending: number;
      completed: number;
    }>
  >(
    `/assessment-task/analysis/group/${enterpriseCode}/${semester}/${month}/${ruleId}`,
    {
      method: 'GET',
    },
  );
}

import { request } from '@umijs/max';

/** 获取指定学校近1年内的问卷列表  GET /questionnaire/${enterpriseCode} */
export async function getQuestionnairesBySchool(enterpriseCode: string) {
  return request<API.ResType<API.IQuestionnaire[]>>(
    `/questionnaire/${enterpriseCode}`,
    {
      method: 'GET',
    },
  );
}

/** 获取指定问卷的教师成绩统计  GET /questionnaire/:questionnaireId/teacher-scores */
export async function getTeacherScoresByQuestionnaire(questionnaireId: string) {
  return request<API.ResType<any[]>>(
    `/questionnaire/${questionnaireId}/teacher-scores`,
    {
      method: 'GET',
    },
  );
}

import { assessmentTaskPoints } from '@/services/assessment_task';
import { envjudge } from '@/utils/calc';
import { LeftOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormList,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import { message, Row } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef } from 'react';
import styles from './index.less';

const formItemLayout = {
  labelCol: { flex: '6em' },
};
const Detail = () => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  const formRef = useRef<ProFormInstance>();
  const { state: info }: any = useLocation();

  const getData = async () => {
    const { errCode, data, msg } = await assessmentTaskPoints({
      taskId: info?.taskId,
    });
    if (errCode) {
      message.warning('获取考核数据失败，' + msg);
    }
    const convertData = (data?.list || []).map((item: any) => {
      const { files, scoreId, remark, observationPoint, pointId, scoreValue } =
        item;
      return {
        id: pointId,
        scoreId,
        name: observationPoint?.pointName,
        score: Number(observationPoint?.baseScore),
        desc: observationPoint?.description,
        realScore: scoreValue ? Number(scoreValue) : undefined,
        remark,
        files: files?.map((file: string, index: number) => {
          return {
            name: '附件' + (index + 1),
            url: '//' + file,
          };
        }),
      };
    });

    formRef.current?.setFieldsValue({
      taskId: info?.taskId,
      assessor: info?.assessedName,
      ruleName: info?.ruleName,
      code: info?.assessedCode,
      scores: convertData,
    });
  };
  useEffect(() => {
    if (info?.taskId) {
      getData();
    }
  }, [info]);
  return (
    <PageContainer
      ghost
      header={{
        title: ' ',
        breadcrumb: {},
        backIcon: (
          <>
            <LeftOutlined />
            返回上一页
          </>
        ),
        onBack: () => history.back(),
      }}
    >
      <div className={styles.fillReport}>
        <h1>考核评分表</h1>
        <div className={styles.itemWrapper}>
          <ProForm
            {...formItemLayout}
            layout="horizontal"
            formRef={formRef}
            grid
            readonly
            submitter={false}
            params={{}}
            initialValues={{
              assessor: '吴楠',
              role: '年级组长',
              timeRange: '2024-01-01 至 2024-03-31',
            }}
          >
            <ProForm.Item>
              <div className={styles.titleBar}>基础信息</div>
            </ProForm.Item>
            <ProForm.Group
              className={classNames(
                styles.itemInfo,
                'card-wrapper',
                styles.greyBg,
              )}
            >
              <ProFormText
                name="assessor"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="被考核人"
                placeholder="请输入名称"
              />
              <ProFormText
                name="code"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="工号"
              />
              <ProFormText
                name="ruleName"
                colProps={{ span: isMobile ? 12 : 8 }}
                readonly
                label="考核分组"
              />
            </ProForm.Group>
            <ProForm.Item colProps={{ span: 24 }}>
              <div className={styles.titleBar}>观测点</div>
            </ProForm.Item>
            <ProFormList
              name="scores"
              creatorButtonProps={false}
              copyIconProps={false}
              deleteIconProps={false}
              alwaysShowItemLabel
            >
              {(_1, _2, action) => {
                const item = action.getCurrentRowData() || {};
                return (
                  <div
                    className={classNames('card-wrapper', styles.borderedBg)}
                    key={item.id}
                  >
                    <div className={styles.itemTitle}>
                      {item.name}
                      <span>（满分{item.score}分）</span>
                    </div>
                    <Row>
                      <ProFormDigit
                        label="评分"
                        labelCol={{ flex: '8em' }}
                        colProps={{ span: isMobile ? 12 : 8 }}
                        name="realScore"
                        min={item.score - 2 < 0 ? 0 : item.score - 2}
                        max={item.score + 2}
                        fieldProps={{ precision: 0 }}
                      />
                      <ProFormTextArea
                        labelCol={{ flex: '8em' }}
                        colProps={{ span: isMobile ? 12 : 8 }}
                        name="remark"
                        label="加减分原因"
                        tooltip={item.desc ? item.desc : undefined}
                      />
                      <ProFormUploadButton
                        labelCol={{ flex: '8em' }}
                        colProps={{ span: 24 }}
                        label="资料"
                        name="files"
                        listType="text"
                        hidden={!item.files}
                        fieldProps={{
                          showUploadList: {
                            showRemoveIcon: false,
                          },
                        }}
                      />
                    </Row>
                  </div>
                );
              }}
            </ProFormList>
          </ProForm>
        </div>
      </div>
    </PageContainer>
  );
};

export default Detail;

import { envjudge } from '@/utils/calc';
import { Collapse, Space } from 'antd';
import React from 'react';

interface AutoLayoutProps {
  className?: string;
  children: React.ReactNode;
  title?: string;
}

const AutoLayout: React.FC<AutoLayoutProps> = ({
  className,
  children,
  title = '搜索条件',
}) => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  return (
    <>
      {isMobile ? (
        <>
          <Collapse
            className={className}
            items={[
              {
                key: '1',
                label: title,
                children,
              },
            ]}
          />
        </>
      ) : (
        <div className={className}>
          <Space>{children}</Space>
        </div>
      )}
    </>
  );
};
export default AutoLayout;

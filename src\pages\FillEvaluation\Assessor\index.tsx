import AutoLayout from '@/components/AutoLayout';
import { assessmentTaskMonth } from '@/services/assessment_task';
import { envjudge, getIsEffect } from '@/utils/calc';
import {
  CheckOutlined,
  ClockCircleOutlined,
  PieChartOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Link, history, useLocation, useModel } from '@umijs/max';
import {
  Breadcrumb,
  Button,
  Col,
  Flex,
  Input,
  Row,
  Space,
  Tag,
  message,
} from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styles from './index.less';

const { Search } = Input;
const Assessor = () => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  const location = useLocation();
  const info = location.state as API.ITaskMonthItme | undefined;
  const { initialState } = useModel('@@initialState');
  const { currentUser, schoolInfo } = initialState || {};
  const [assessorList, setAssessorList] = useState<API.IAssessmentTask[]>([]);
  const [assessorCount, setAssessorCount] = useState<any>();
  const [status] = useState<string>('all');
  const [assessedName, setAssessedName] = useState<string>('');
  const getData = async () => {
    if (!info || !schoolInfo?.code) {
      return;
    }
    const { errCode, data, msg } = await assessmentTaskMonth({
      assessorCode: currentUser?.userCode,
      enterpriseCode: schoolInfo && schoolInfo.code!,
      semester: info?.semester,
      month: info?.month,
    });
    if (errCode) {
      message.warning('获取考核人员列表数据失败，' + msg);
      setAssessorList([]);
    }
    const total = data?.list?.length || 0;
    const completedCount = data?.list?.filter(
      (item: any) => item.status === 'completed',
    )?.length;
    setAssessorCount({
      total,
      isPub: !!data?.pubPlans?.planId,
      notCompletedCount: total - (completedCount || 0),
      completedCount: completedCount,
      rate: ((Number(completedCount) / Number(total)) * 100).toFixed(2),
    });
    setAssessorList(data?.list || []);
  };
  useEffect(() => {
    if (info && schoolInfo?.code) {
      getData();
    }
  }, [info]);

  if (!info) {
    return null;
  }

  return (
    <div className={styles.assessorPage}>
      <Breadcrumb
        items={[
          { title: <Link to="/fillEvaluation/list">考核填报</Link> },
          { title: `${info.year}年${info.month}月月度考核填报` },
        ]}
      />
      {!isMobile && (
        <div className={classNames('card-wrapper', styles.itemHeader)}>
          <Row
            style={{
              width: '100%',
            }}
            justify={'space-between'}
          >
            <Col span={isMobile ? 24 : 12}>
              <h1>
                {info.year}年{info.month}月月度考核填报
                <Space
                  style={{
                    marginInlineStart: 16,
                  }}
                >
                  {assessorCount?.isPub && <Tag color="#87d068">已公示</Tag>}
                </Space>
              </h1>
              <p className={classNames('lightColor', styles.endTime)}>
                填报日期：
                {!info.fillableDates.length ? (
                  '整月'
                ) : (
                  <>
                    <span>
                      {dayjs(info.fillableDates?.[0]).format('MM月DD日')}
                    </span>
                    {info.fillableDates.length > 1 && (
                      <span>
                        &nbsp;&nbsp;至&nbsp;&nbsp;
                        {dayjs(
                          info.fillableDates[info.fillableDates.length - 1],
                        ).format('MM月DD日')}
                      </span>
                    )}
                  </>
                )}
              </p>
            </Col>
          </Row>
        </div>
      )}

      <div className={styles.itemContent}>
        <Row gutter={[24, 24]}>
          <Col span={8}>
            <div
              className={classNames(
                { ['card-wrapper']: !isMobile },
                styles.itemCard,
                styles.blue,
              )}
              style={{
                justifyContent: isMobile ? 'center' : 'space-between',
              }}
            >
              <div>
                <p className={'lightColor'}>待处理考核</p>
                <h2>{assessorCount?.notCompletedCount}</h2>
              </div>

              {!isMobile && (
                <div>
                  <ClockCircleOutlined />
                </div>
              )}
            </div>
          </Col>
          <Col span={8}>
            <div
              className={classNames(
                { ['card-wrapper']: !isMobile },
                styles.itemCard,
                styles.green,
              )}
              style={{
                justifyContent: isMobile ? 'center' : 'space-between',
              }}
            >
              <div>
                <p className={'lightColor'}>已处理考核</p>
                <h2>{assessorCount?.completedCount}</h2>
              </div>

              {!isMobile && (
                <div>
                  <CheckOutlined />
                </div>
              )}
            </div>
          </Col>
          <Col span={8}>
            <div
              className={classNames(
                { ['card-wrapper']: !isMobile },
                styles.itemCard,
                styles.yellow,
              )}
              style={{
                justifyContent: isMobile ? 'center' : 'space-between',
              }}
            >
              <div>
                <p className={'lightColor'}>考核完成率</p>
                <h2>
                  {assessorCount?.rate}
                  {assessorCount && '%'}
                </h2>
              </div>
              {!isMobile && (
                <div>
                  <PieChartOutlined />
                </div>
              )}
            </div>
          </Col>
        </Row>

        <AutoLayout className={styles.searchWrapper}>
          <Row gutter={16}>
            <Col
              span={isMobile ? 24 : undefined}
              style={{ marginBottom: isMobile ? 24 : undefined }}
            >
              <Search
                placeholder="搜索考核对象"
                allowClear
                onSearch={setAssessedName}
                style={{ width: isMobile ? '100%' : 300 }}
              />
            </Col>
          </Row>
        </AutoLayout>

        <Row gutter={[24, 24]} wrap>
          {assessorList
            ?.filter((item: any) => {
              let flag1 = item.assessedName.includes(assessedName);
              let flag2 = item.status === status || status === 'all';
              return flag1 && flag2;
            })
            .map((item) => {
              return (
                <Col key={item.taskId} xxl={4} lg={6} md={12} xs={24}>
                  <div
                    className={classNames('card-wrapper', styles.peopleCard)}
                  >
                    <Flex align="center" justify="space-between">
                      <h2>{item.assessedName}</h2>
                      <Tag color={item.status === 'pending' ? 'blue' : 'green'}>
                        {item.status === 'pending' ? '进行中' : '已完成'}
                      </Tag>
                    </Flex>
                    <p className={classNames('lightColor')}>
                      工号：{item.assessedCode}
                    </p>
                    <p className={styles.descInfo}>
                      <TeamOutlined />
                      考核分组：{item.ruleName}
                    </p>
                    {item.status === 'pending' ? (
                      <Button
                        type="primary"
                        disabled={
                          !getIsEffect(info?.fillableDates || []) ||
                          assessorCount?.isPub
                        }
                        onClick={() => {
                          history.push(`/fillEvaluation/fillReport`, item);
                        }}
                      >
                        立即填写
                      </Button>
                    ) : (
                      <Button
                        onClick={() => {
                          history.push(
                            `/fillEvaluation/fillReport/detail`,
                            item,
                          );
                        }}
                      >
                        查看详情
                      </Button>
                    )}
                  </div>
                </Col>
              );
            })}
        </Row>
      </div>
    </div>
  );
};

export default Assessor;

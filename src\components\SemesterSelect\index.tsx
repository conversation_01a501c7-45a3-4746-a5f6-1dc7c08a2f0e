import { ProFormSelectProps } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Flex, Select } from 'antd';
import React from 'react';

interface SemesterSelectProps extends ProFormSelectProps {
  semesterChange?: (value: string) => void;
}

const SemesterSelect: React.FC<SemesterSelectProps> = ({ semesterChange }) => {
  const { semesterData } = useModel('global');

  return (
    <Flex align="center">
      <label>学年学期：</label>
      <Select
        defaultValue={semesterData?.currentSemester || ''}
        style={{ width: 200 }}
        options={(semesterData?.semesterList || []).sort((a, b) =>
          b.value.localeCompare(a.value),
        )}
        onChange={(value: string) => {
          semesterChange?.(value);
        }}
      />
    </Flex>
  );
};

export default SemesterSelect;

import {
  ModalForm,
  ModalFormProps,
  ProCard,
  ProFormDigit,
  ProFormInstance,
  ProFormList,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import React, { useRef } from 'react';

interface AddObservationModalProps extends ModalFormProps {
  onCancel: () => void;
  data?: API.IObservationPoint[];
}

const AddObservationModal: React.FC<AddObservationModalProps> = ({
  onCancel,
  data,
  ...restProps
}) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      width={650}
      labelCol={{ span: 4 }}
      formRef={formRef}
      title={data ? '编辑观测点' : '新增观测点'}
      {...restProps}
      layout="horizontal"
      modalProps={{
        destroyOnClose: true,
        maskClosable: false,
        onCancel,
        styles: {
          body: {
            marginTop: '20px',
            maxHeight: '500px',
            overflowY: 'auto',
            paddingRight: '5px',
          },
        },
      }}
    >
      <ProFormList
        name="list"
        creatorButtonProps={{
          creatorButtonText: '添加观测点',
        }}
        min={1}
        copyIconProps={false}
        itemRender={({ listDom, action }, { index }) => (
          <ProCard
            bordered
            style={{ marginBlockEnd: 8 }}
            title={`观测点 ${index + 1}`}
            extra={action}
            bodyStyle={{ paddingBlockEnd: 0 }}
          >
            {listDom}
          </ProCard>
        )}
        initialValue={
          data || [
            {
              pointName: '',
              baseScore: undefined,
              description: '',
            },
          ]
        }
      >
        <Row gutter={16}>
          <Col span={12}>
            <ProFormText
              name="pointName"
              label="名称"
              rules={[{ required: true, message: '请输入观测点名称' }]}
              placeholder="请输入观测点名称"
            />
          </Col>
          <Col span={12}>
            <ProFormDigit
              name="baseScore"
              label="分值"
              min={0}
              max={100}
              fieldProps={{ precision: 0, suffix: '分' }}
              rules={[{ required: true, message: '请输入0-100的整数分值' }]}
            />
          </Col>
        </Row>

        <ProFormText
          labelCol={{
            flex: '3.7em',
          }}
          name="description"
          label="说明"
          placeholder="请输入打分说明"
          fieldProps={{ maxLength: 200 }}
        />
      </ProFormList>
    </ModalForm>
  );
};
export default AddObservationModal;

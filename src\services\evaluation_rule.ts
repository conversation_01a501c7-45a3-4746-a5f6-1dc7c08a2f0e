/** 考核规则 */
import { request } from '@umijs/max';

/** 查询规列  GET /assessment-plans/:planId/rule */
export async function getAllEvaluationRuleAPI(planId: number) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/assessment-plans/${planId}/rule`,
    {
      method: 'GET',
    },
  );
}

/** 创建规则  POST /assessment-plans/:planId/rule */
export async function createEvaluationRuleAPI(planId: number, body: API.IRule) {
  return request<API.ResType>(`/assessment-plans/${planId}/rule`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 修改考核规则  PUT /assessment-plans/:planId:/:ruleId*/
export async function updateEvaluationRuleAPI(
  planId: number,
  ruleId: number,
  body: any,
) {
  return request<API.ResType<unknown>>(
    `/assessment-plans/${planId}/rule/${ruleId}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 删除考核规则  DELETE /assessment-plans/:planId/rule/:ruleId*/
export async function removeEvaluationRuleAPI(planId: number, ruleId: number) {
  return request<API.ResType<unknown>>(
    `/assessment-plans/${planId}/rule/${ruleId}`,
    {
      method: 'DELETE',
    },
  );
}

/**检查规则配置完整性 */
export async function checkEvaluationRuleAPI(planId: number, ruleId: number) {
  return request<API.ResType<unknown>>(
    `/assessment-plans/${planId}/rule/${ruleId}/check`,
    {
      method: 'GET',
    },
  );
}

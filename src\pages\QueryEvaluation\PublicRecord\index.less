.publicRecordDetail {
  .roleBread {
    :global {
      .ant-segmented-group {
        .ant-segmented-item {
          padding: 2px 8px;

          &.ant-segmented-item-selected {
            background-color: var(--primary-color);
            color: #fff;
          }
        }
      }
    }
  }

  :global {
    .ant-table-container {
      .ant-table-thead {
        padding: 4px 0;

        .ant-table-cell {
          background-color: #efefef;
        }
      }

      .ant-table-cell {
        padding: 0 8px !important;
        line-height: 30px;
      }

      table,
      thead,
      tbody,
      tr,
      td,
      th {
        border-color: #dadada !important;
      }
    }
  }
}

.scoreInfoModal {
  .score {
    line-height: 40px;

    span {
      color: var(--primary-color);
      font-weight: bold;
      font-size: 18px;
    }
  }
}

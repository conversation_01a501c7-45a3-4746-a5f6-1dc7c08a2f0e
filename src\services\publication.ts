import { request } from '@umijs/max';

/** 获取当前有效的公示记录  GET /publication/actived/{enterpriseCode} */
export async function getPublicityListAPI(
  enterpriseCode: string,
  params: Record<string, any>,
) {
  return request<
    API.ResType<{ total?: number; list?: API.IPublicationRecord[] }>
  >(`/publication/actived/${enterpriseCode}`, {
    method: 'GET',
    params,
  });
}

/** 发布公示记录 POST  /publication/pub/{enterpriseCode}/{month} */
export async function publishPublicityAPI(
  enterpriseCode: string,
  month: string,
  semester: string,
  body: {
    startTime: string;
    endTime: string;
  },
) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/publication/pub/${enterpriseCode}/${semester}/${month}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 撤销公示记录 POST /publication/unpub/{enterpriseCode}/{semester}/{month} */
export async function revokePublicityAPI({
  enterpriseCode,
  semester,
  month,
}: {
  enterpriseCode: string;
  semester: string;
  month: string;
}) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/publication/unpub/${enterpriseCode}/${semester}/${month}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
}

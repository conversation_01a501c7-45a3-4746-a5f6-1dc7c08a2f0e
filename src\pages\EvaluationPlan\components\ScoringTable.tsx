import React, { useEffect, useState } from 'react';

import {
  checkEvaluationScoringAPI,
  createEvaluationScoringAPI,
  getAllEvaluationScoringAPI,
  removeEvaluationScoringAPI,
  updateEvaluationScoringAPI,
} from '@/services/evaluation_scoring';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import AddScoringModal from './AddScoringModal';
import { Level, scoringColumns } from './columns';

interface ScoringTableProps {
  ruleId: number | undefined;
  handleTemplateClick: ({
    next,
    prev,
    data,
  }: {
    next?: Level;
    prev?: Level;
    data?: API.IScoring;
  }) => void;
  title: string;
  readonly: boolean;
}

const ScoringTable: React.FC<ScoringTableProps> = ({
  ruleId,
  title,
  handleTemplateClick,

  readonly,
}) => {
  const [scoringModalOpen, setScoringModalOpen] = useState({
    open: false,
    data: undefined,
  });
  const [dataSource, setDataSource] = useState<API.IScoring[]>([]);
  /** 查询考核方案是否配置完整考核规则  */
  const [checkScoringId, setCheckScoringId] = useState<number[]>([]);
  const [checkingStatus, setCheckingStatus] = useState<
    Record<
      string,
      {
        status: 'checking' | 'complete' | 'incomplete' | 'error';
        retry?: () => void;
      }
    >
  >({});

  /** 获取考核列表 */
  const getAllEvaluationscoringList = async (isCheck = false) => {
    if (!ruleId) return { data: [], success: false };
    const { errCode, msg, data } = await getAllEvaluationScoringAPI(ruleId);

    if (errCode) {
      message.warning('获取考核列表数据失败，' + msg);
      setDataSource([]);
      return;
    }

    if (data?.list && data?.list?.length) {
      const initialList = data.list.map((item: any) => {
        // 保留之前的规则检测状态
        const existingItem: any = dataSource.find(
          (ds) => ds.scoringId === item.scoringId,
        );
        const rulesStatus =
          existingItem?.rulesStatus || (isCheck ? 'checking' : 'unchecked');
        const rules = existingItem?.rules || (isCheck ? '检查中...' : '未检查');
        return {
          ...item,
          rules,
          rulesStatus,
        };
      });

      if (isCheck) {
        const scoringIds = initialList.map((item: any) => item?.scoringId);
        setCheckScoringId(scoringIds);
        const initialStatus: Record<
          string,
          {
            status: 'checking' | 'complete' | 'incomplete' | 'error';
            retry?: () => void;
          }
        > = {};
        scoringIds.forEach((scoringId) => {
          initialStatus[scoringId] = { status: 'checking' };
        });
        setCheckingStatus(initialStatus);
      }
      setDataSource(initialList);
    } else {
      setDataSource([]);
    }
  };

  /** 新增 */
  const onAddScoring = async (values: API.IScoring): Promise<boolean> => {
    if (!ruleId) return false;
    const { errCode, msg } = await createEvaluationScoringAPI(ruleId, values);
    if (errCode) {
      message.warning('新增赋分规则失败，' + msg);
      return false;
    }
    message.success('新增赋分规则成功');
    getAllEvaluationscoringList(true);
    setScoringModalOpen({ open: false, data: undefined });
    return true;
  };

  /** 编辑 */
  const onEditScoring = async (values: API.IScoring): Promise<boolean> => {
    if (!values.scoringId) {
      message.warning('编辑失败，scoringId 不存在');
      return false;
    }
    if (!ruleId) {
      message.warning('编辑失败，ruleId 不存在');
      return false;
    }
    if (!values) {
      message.warning('编辑失败，编辑内容不能为空');
      return false;
    }
    const { errCode, msg } = await updateEvaluationScoringAPI(
      ruleId,
      values?.scoringId,
      {
        ...values,
        scoringId: undefined,
        users: values?.assessorType === 'user' ? values?.users : [],
        roles: values?.assessorType === 'role' ? values?.roles : [],
      },
    );
    if (errCode) {
      message.warning('编辑赋分规则失败，' + msg);
      return false;
    }
    message.success('编辑赋分规则成功');
    setScoringModalOpen({
      open: false,
      data: undefined,
    });
    getAllEvaluationscoringList();
    return true;
  };

  /** 删除 */
  const onDelete = async (values: API.IScoring) => {
    if (!ruleId) {
      return message.warning('删除失败，ruleId 不存在');
    }
    if (!values?.scoringId) {
      return message.warning('删除失败，scoringId 不存在');
    }

    const { errCode, msg } = await removeEvaluationScoringAPI(
      ruleId,
      values.scoringId,
    );
    if (errCode) {
      message.warning('删除赋分规则失败，' + msg);
    }
    message.success('删除赋分规则成功');
    getAllEvaluationscoringList();
  };

  const updateDataSourceWithStatus = (
    statusMap: Record<string, { status: string; retry?: () => void }>,
  ) => {
    const updatedDataSource = dataSource.map((item) => {
      const status = statusMap[item.scoringId];
      if (!status) return item;
      let rulesText = '检查中...';
      if (status.status === 'complete') {
        rulesText = '规则完整';
      } else if (status.status === 'incomplete') {
        rulesText = '规则不完整';
      } else if (status.status === 'error') {
        rulesText = '检查失败，点击重试';
      }

      return {
        ...item,
        rules: rulesText,
        rulesStatus: status.status,
        retryCheck: status.retry,
      };
    });

    setDataSource(updatedDataSource);
  };

  /** 检查考核规则是否完整 */
  const checkRules = async (scoringIdList: number[]) => {
    if (!scoringIdList?.length || !ruleId) {
      return message.warning('检查失败，ruleId 或 scoringIdListList 不存在');
    }
    const initialStatus = { ...checkingStatus };
    scoringIdList.forEach((scoringId) => {
      initialStatus[scoringId] = { status: 'checking' };
    });
    setCheckingStatus(initialStatus);
    const checkPromises = scoringIdList.map((scoringId) =>
      checkEvaluationScoringAPI(ruleId, scoringId),
    );
    try {
      const results = await Promise.allSettled(checkPromises);
      const newStatus = { ...initialStatus };
      results.forEach((result, index) => {
        const scoringId = scoringIdList[index];
        if (result.status === 'rejected') {
          newStatus[scoringId] = {
            status: 'error',
            retry: () => checkRules([scoringId]),
          };
        } else {
          const response = result.value;
          if (response.errCode) {
            newStatus[scoringId] = {
              status: 'error',
              retry: () => checkRules([scoringId]),
            };
          } else {
            const isComplete = !!response.data;
            newStatus[scoringId] = {
              status: isComplete ? 'complete' : 'incomplete',
              retry: isComplete ? undefined : () => checkRules([scoringId]),
            };
          }
        }
      });
      setCheckingStatus(newStatus);
      updateDataSourceWithStatus(newStatus);
    } catch (error) {
      console.error('检查方案完整性时出错:', error);
      return message.warning('检查方案完整性时出错' + error);
    }
  };

  const handEvent = (type: string, data: any) => {
    switch (type) {
      case 'edit':
        setScoringModalOpen({ open: true, data });
        break;
      case 'remove':
        onDelete(data);
        break;
      case 'observation':
        handleTemplateClick({
          next: 'observation',
          prev: 'scoring',
          data,
        });
        break;
      case 'retry':
        checkRules([data.ruleId]);
    }
  };

  useEffect(() => {
    if (ruleId) {
      getAllEvaluationscoringList(true);
    }
  }, [ruleId]);

  useEffect(() => {
    if (checkScoringId && checkScoringId?.length > 0) {
      checkRules(checkScoringId);
    }
  }, [checkScoringId]);

  return (
    <>
      <ProTable
        headerTitle={title || '赋分规则'}
        columns={scoringColumns({
          handEvent,
          isDelleteShow: !readonly,
        })}
        dataSource={dataSource}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        toolBarRender={() => [
          <>
            {!readonly && (
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  setScoringModalOpen({
                    open: true,
                    data: undefined,
                  });
                }}
                type="primary"
              >
                新增赋分规则
              </Button>
            )}
          </>,
        ]}
      />
      <AddScoringModal
        open={scoringModalOpen.open}
        data={scoringModalOpen.data}
        isReadonly={readonly}
        onSubmit={!!scoringModalOpen.data ? onEditScoring : onAddScoring}
        onCancel={() => {
          setScoringModalOpen({
            open: false,
            data: undefined,
          });
        }}
      />
    </>
  );
};
export default ScoringTable;

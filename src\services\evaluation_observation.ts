import { request } from '@umijs/max';

/** 查询观测点  GET /scoring/:scoringId/observation-points */
export async function getAllEvaluationObservationPointAPI(
  scoringId: number | undefined,
) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/scoring/${scoringId}/observation-points`,
    {
      method: 'GET',
    },
  );
}

/** 创建观测点  POST /scoring/:scoringId/observation-points */
export async function createEvaluationObservationPointAPI(
  scoringId: number,
  body: {
    list: Partial<API.IObservationPoint>[];
  },
) {
  return request<API.ResType>(`/scoring/${scoringId}/observation-points`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
/** 更新观测点  PUT /scoring/:scoringId/observation-points/:id */
export async function updateEvaluationObservationPointAPI(
  scoringId: number,
  id: number,
  body: Partial<API.IObservationPoint>,
) {
  return request<API.ResType<unknown>>(
    `/scoring/${scoringId}/observation-points/${id}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 删除观测点  DELETE /scoring/:scoringId/observation-points/:id */
export async function removeEvaluationObservationPointAPI(
  scoringId: number,
  id: number,
) {
  return request<API.ResType<unknown>>(
    `/scoring/${scoringId}/observation-points/${id}`,
    {
      method: 'DELETE',
    },
  );
}

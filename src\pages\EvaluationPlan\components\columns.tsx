import { ProColumns } from '@ant-design/pro-components';
import { Button, Popconfirm, Space, Tag, Tooltip } from 'antd';

export type Level = 'scoring' | 'rules' | 'observation';

interface RuleColumns {
  handEvent: (type: string, data: API.IRule) => void;
  isDelleteShow?: boolean;
}
interface ScoringColumns {
  handEvent: (type: string, data: API.IScoring) => void;
  isDelleteShow?: boolean;
}
interface ObservationColumns {
  isDelleteShow?: boolean;
  handEvent: (type: string, data: API.IObservationPoint) => void;
}

/** 考核规则 */
export const ruleColumns = ({
  handEvent,
  isDelleteShow,
}: RuleColumns): ProColumns[] => {
  return [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 48,
    },
    {
      title: '名称',
      align: 'center',
      dataIndex: 'title',
    },
    {
      title: '参评人员类型',
      align: 'center',
      dataIndex: 'assessedType',
      valueEnum: {
        role: '身份',
        user: '人员',
      },
    },
    {
      title: '参评人员',
      align: 'center',
      dataIndex: 'peopelList',
      render: (_, record) => {
        const isMax = 3;
        if (record?.assessedType === 'role') {
          const isMaxLength = record?.roles?.length > isMax;
          const mapData = record?.roles
            ?.map((item: any) => item.roleName)
            .join('、');
          const sliceMap =
            record?.roles
              ?.map((item: any) => item.roleName)
              .slice(0, isMax)
              .join('、') + '...';
          return (
            <Tooltip title={mapData}>
              <Space>{isMaxLength ? sliceMap : mapData}</Space>
            </Tooltip>
          );
        } else {
          const isMaxLength = record?.users?.length > isMax;
          const mapData = record?.users
            ?.map((item: any) => item.userName)
            .join('、');
          const sliceMap =
            record?.users?.map((item: any) => item.userName).slice(0, isMax) +
            '...';
          return (
            <Tooltip title={mapData}>
              <Space>{isMaxLength ? sliceMap : mapData}</Space>
            </Tooltip>
          );
        }
      },
    },
    {
      title: '赋分规则',
      align: 'center',
      dataIndex: 'scorings',
      render: (_dom, record) => {
        switch (record.rulesStatus) {
          case 'checking':
            return <span>检查中...</span>;
          case 'complete':
            return <Tag color="#108ee9">规则完整</Tag>;
          case 'incomplete':
            return <Tag color="#FFB833">规则不完整</Tag>;
          case 'error':
            return (
              <Button type="link" onClick={() => handEvent('retry', record)}>
                重试
              </Button>
            );
          default:
            return '-';
        }
      },
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 200,
      render: (text, record) => {
        return (
          <Space>
            <Button
              size="small"
              type="link"
              onClick={() => {
                handEvent('edit', record);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => handEvent('scoring', record)}
            >
              设置打分人员
            </Button>
            {isDelleteShow && (
              <Popconfirm
                title="您确定要删除此条考核规则吗？"
                description="注意：删除后无法恢复，请谨慎操作。"
                onConfirm={() => {
                  handEvent('remove', record);
                }}
              >
                <Button size="small" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];
};

/** 赋分规则 */
export const scoringColumns = ({
  handEvent,
  isDelleteShow,
}: ScoringColumns): ProColumns[] => {
  return [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 48,
    },
    {
      title: '名称',
      align: 'center',
      dataIndex: 'title',
    },
    {
      title: '评分人员类型',
      align: 'center',
      dataIndex: 'assessorType',
      valueEnum: {
        role: '身份',
        user: '人员',
        question: '问卷',
      },
    },
    {
      title: '评分人员',
      align: 'center',
      dataIndex: 'roleNames',
      render: (_, record) => {
        const isMax = 3;
        if (record?.assessorType === 'role') {
          const isMaxLength = record?.roles?.length > isMax;
          const mapData = record?.roles
            ?.map((item: any) => item.roleName)
            .join('、');
          const sliceMap =
            record?.roles
              ?.map((item: any) => item.roleName)
              .slice(0, isMax)
              .join('、') + '...';
          return (
            <Tooltip title={mapData}>
              <Space>{isMaxLength ? sliceMap : mapData}</Space>
            </Tooltip>
          );
        } else if (record?.assessorType === 'user') {
          const isMaxLength = record?.users?.length > isMax;
          const mapData = record?.users
            ?.map((item: any) => item.userName)
            .join('、');
          const sliceMap =
            record?.users?.map((item: any) => item.userName).slice(0, isMax) +
            '...';
          return (
            <Tooltip title={mapData}>
              <Space>{isMaxLength ? sliceMap : mapData}</Space>
            </Tooltip>
          );
        } else {
          return '-';
        }
      },
    },
    {
      title: '权重',
      align: 'center',
      dataIndex: 'weight',
      render: (_, record) => {
        return `${record?.weight}%`;
      },
    },
    {
      title: '观测点',
      align: 'center',
      dataIndex: 'scorings',
      render: (_dom, record) => {
        switch (record.rulesStatus) {
          case 'checking':
            return <span>检查中...</span>;
          case 'complete':
            return <Tag color="#108ee9">已配置</Tag>;
          case 'incomplete':
            return <Tag color="#FFB833">未配置</Tag>;
          case 'error':
            return (
              <Button type="link" onClick={() => handEvent('retry', record)}>
                重试
              </Button>
            );
          default:
            return '-';
        }
      },
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 200,
      render: (_dom, record) => {
        return (
          <Space>
            <Button
              size="small"
              type="link"
              onClick={() => handEvent('edit', record)}
            >
              编辑
            </Button>
            {record?.assessorType !== 'question' && (
              <Button
                size="small"
                type="link"
                onClick={() => handEvent('observation', record)}
              >
                设置观测点
              </Button>
            )}
            {isDelleteShow && (
              <Popconfirm
                title="您确定要删除此条赋分规则吗？"
                description="注意：删除后无法恢复，请谨慎操作。"
                onConfirm={() => {
                  handEvent('remove', record);
                }}
              >
                <Button size="small" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];
};

/** 观测点 */
export const observationColumns = ({
  handEvent,
  isDelleteShow,
}: ObservationColumns): ProColumns[] => {
  return [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 48,
    },
    {
      title: '名称',
      align: 'center',
      dataIndex: 'pointName',
    },
    {
      title: '分值',
      align: 'center',
      dataIndex: 'baseScore',
    },
    {
      title: '打分说明',
      align: 'center',
      dataIndex: 'description',
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      render: (_dom, record) => {
        return (
          <Space>
            <Button
              size="small"
              type="link"
              onClick={() => {
                handEvent('edit', record);
              }}
            >
              编辑
            </Button>

            {isDelleteShow && (
              <Popconfirm
                title="您确定要删除此条赋分规则吗？"
                description="注意：删除后无法恢复，请谨慎操作。"
                onConfirm={() => {
                  handEvent('remove', record);
                }}
              >
                <Button size="small" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];
};

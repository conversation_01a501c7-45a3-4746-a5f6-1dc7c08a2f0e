import { defineConfig } from '@umijs/max';
import routes from './route';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '教师月度考核',
  },
  theme: {
    primaryColor: '#2F82FF', // 这里可以设置你想要的主色调
  },
  define: {
    TENCENT_COS: {
      SecretId: 'AKIDJRL2VjjU2JL7g06xZy84lErGSREl391e',
      SecretKey: 'ShhjY9WgfR1OuKVY1KPJ0SK0SgjY9e0B',
      baseDir: 'teacher-evaluation',
    },
  },
  routes,
  npmClient: 'pnpm',
  proxy: {
    '/api_evaluate': {
      // target: 'http://49.233.193.39:3140',
      target: 'http://192.168.0.100:3140',
      changeOrigin: true,
      pathRewrite: { '^/api_evaluate': '' },
    },
    '/edu_api': {
      target: 'http://49.233.193.39:3040',
      changeOrigin: true,
      pathRewrite: { '^/edu_api': '' },
    },
    '/api_common': {
      // target: 'http://request.test.xingjiaoyun.cn',
      target: 'http://49.233.193.39:1002',
      changeOrigin: true,
      pathRewrite: { '^/api_common': '' },
    },
  },
});

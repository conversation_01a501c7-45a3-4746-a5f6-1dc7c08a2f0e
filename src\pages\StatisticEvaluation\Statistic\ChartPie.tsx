import { Empty } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface ChartPieProps {
  data: {
    type: string;
    value: number;
  }[];
}

const ChartPie: React.FC<ChartPieProps> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!Array.isArray(data) || data.length === 0 || !chartRef.current) return;

    // 初始化 ECharts 实例
    const chart = echarts.init(chartRef.current);

    // 配置项
    const option = {
      color: ['#1890ff', '#36cfc9', '#ff4d4f', '#ffa940', '#9254de'],
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const { name, value, percent } = params;
          return `${name}: ${value} (${percent}%)`;
        },
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'middle',
      },
      series: [
        {
          name: '数据',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside', // 标签显示在外部
            formatter: '{b}: {c}个',
            fontWeight: 'bold',
          },
          labelLine: {
            show: true, // 显示引导线
            length: 10, // 第一段线长度
            length2: 15, // 第二段线长度
            smooth: true, // 平滑曲线
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold',
            },
          },
          data: data.map((item) => ({
            name: item.type,
            value: item.value,
          })),
        },
      ],
    };

    // 设置配置项
    chart.setOption(option);

    // 组件卸载时销毁实例
    return () => {
      chart.dispose();
    };
  }, [data]);

  if (!Array.isArray(data)) {
    throw new Error('data is not an array');
  }

  if (data.length === 0) {
    return (
      <Empty
        style={{
          marginTop: 80,
        }}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div
      ref={chartRef}
      style={{
        width: '100%',
        height: '200px',
      }}
    />
  );
};

export default ChartPie;

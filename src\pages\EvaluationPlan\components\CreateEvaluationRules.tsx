import { Breadcrumb, Drawer } from 'antd';
import React, { useEffect, useState } from 'react';
import { Level } from './columns';
import styles from './index.less';
import ObservationTable from './ObservationTable'; // 新增导入
import RulesTable from './RulesTable';
import ScoringTable from './ScoringTable';

interface CreateEvaluationRulesPros {
  open: boolean;
  data: any;
  onClose: () => void;
}

const CreateEvaluationRules: React.FC<CreateEvaluationRulesPros> = ({
  open,
  data,
  onClose,
}) => {
  const { planId } = data ?? {};
  const [level, setLevel] = useState<Level>('rules');
  const [ruleId, setRuleId] = useState<number>();
  const [scoringId, setScoringId] = useState<number>();
  const [ruleTitle, setRuleTitle] = useState<string>('');
  const [scoringTitle, setScoringTitle] = useState<string>('');

  // 修改面包屑项类型定义
  const [breadcrumbItems, setBreadcrumbItems] = useState<
    Array<{
      title: React.ReactNode;
      path?: string;
      onClick?: () => void;
    }>
  >([{ title: '考核规则' }]);

  const resetBreadcrumb = () => {
    setLevel('rules');
    setBreadcrumbItems([{ title: '考核规则' }]);
    setRuleId(undefined);
    setScoringId(undefined);
    setRuleTitle('');
    setScoringTitle('');
  };

  const handleTemplateClick = ({
    next,
    data,
  }: {
    next?: Level;
    prev?: Level;
    data?: any;
  }) => {
    if (!next) return;

    if (level === 'rules' && next === 'scoring' && data && 'ruleId' in data) {
      setRuleId(data.ruleId);
      setRuleTitle(data.title || '');
    } else if (
      level === 'scoring' &&
      next === 'observation' &&
      data &&
      'scoringId' in data
    ) {
      setScoringId(data.scoringId);
      setScoringTitle(data.scoringName || data.title || '');
    }

    const newItems = [
      {
        title: '考核规则',
        onClick: () => resetBreadcrumb(),
      },
    ];
    if (next === 'scoring') {
      newItems.push({
        title: '赋分规则',
        onClick: () => {},
      });
    } else if (next === 'observation') {
      newItems.push(
        {
          title: '赋分规则',
          onClick: () => {
            setLevel('scoring');
            setBreadcrumbItems([
              {
                title: '考核规则',
                onClick: () => resetBreadcrumb(),
              },
              { title: '赋分规则' },
            ]);
          },
        },
        {
          title: '观测点',
          onClick: () => {},
        },
      );
    }
    setBreadcrumbItems(newItems);
    setLevel(next);
  };

  const readonly =
    data?.rulesStatus === 'complete' && data?.status === 'published';

  const renderContent = () => {
    switch (level) {
      case 'rules':
        return (
          <RulesTable
            planId={planId}
            handleTemplateClick={handleTemplateClick}
            title={data?.planName || ''}
            readonly={readonly}
          />
        );
      case 'scoring':
        return (
          <ScoringTable
            ruleId={ruleId}
            handleTemplateClick={handleTemplateClick}
            title={ruleTitle}
            readonly={readonly}
          />
        );
      case 'observation':
        return (
          <ObservationTable
            scoringId={scoringId}
            title={scoringTitle}
            readonly={readonly}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (open) {
      resetBreadcrumb();
    }
  }, [open]);

  return (
    <>
      <Drawer
        width="60vw"
        open={open}
        destroyOnClose
        maskClosable={false}
        onClose={onClose}
        title="设置被考核人员"
        className={styles.createEvaluationRules}
      >
        <Breadcrumb items={breadcrumbItems} className={styles.breadcrumb} />
        {renderContent()}
      </Drawer>
    </>
  );
};
export default CreateEvaluationRules;

import pubilcImg from '@/assets/public.png';
import AutoLayout from '@/components/AutoLayout';
import NoData from '@/components/NoData';
import { getAssessmentTaskGroups } from '@/services/assessment_task';
import { getPublicityListAPI } from '@/services/publication';
import { getReports } from '@/services/report';
import { envjudge } from '@/utils/calc';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Col, Input, message, Row, Select } from 'antd';
import { useEffect, useState } from 'react';
import getColumns from './columns';
import styles from './index.less';
import ScoreInfo from './ScoreInfo';

const { Search } = Input;
const PublicRecord = () => {
  const env_screen = envjudge();
  const isMobile = env_screen.includes('mobile');
  const { initialState } = useModel('@@initialState');
  const { semesterData } = useModel('global');

  const { schoolInfo } = initialState || {};
  const [isShow, setIsShow] = useState<boolean>(false);
  const [groups, setGroups] = useState<{ label: string; value: string }[]>([]);
  /** 当前显示的公示信息对应的考核月份 */
  const [month, setMonth] = useState<number>();
  const [role, setRole] = useState<{ label: string; value: string }>();
  /** 动态设置观测点列 */
  const [pontColumns, setPontColumns] = useState<API.IScoreReportDetail[]>([]);
  const [filterText, setFilterText] = useState<string>('');
  /** 得分详情弹窗 */
  const [modalData, setModalData] = useState<{
    isModalOpen: boolean;
    detail?: API.IScoreReport;
  }>({
    isModalOpen: false,
  });

  // 获取公示信息
  const getPublication = async ({
    enterpriseCode,
  }: {
    enterpriseCode: string;
  }) => {
    const { errCode, msg, data } = await getPublicityListAPI(
      enterpriseCode,
      {},
    );
    if (errCode) {
      message.warning(msg || '查询失败');
      return;
    }
    if (data?.list?.length) {
      const info = data.list[0];
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const start = new Date(info?.startTime);
      start.setHours(0, 0, 0, 0);
      const end = new Date(info?.endTime);
      end.setHours(0, 0, 0, 0);
      if (today >= start && today <= end) {
        setIsShow(true);
        setMonth(info.month);
        return;
      }
    }
    setIsShow(false);
  };

  // 获取考核分类
  const getGroups = async (params: {
    enterpriseCode: string;
    semester: string;
    month: number;
  }) => {
    const { errCode, msg, data } = await getAssessmentTaskGroups(params);
    if (errCode) {
      message.warning(msg || '查询失败');
      return;
    }
    const list =
      data?.list?.map((item) => ({
        label: item.ruleName,
        value: String(item.ruleId),
      })) || [];
    setGroups(list);
    setRole(list?.[0]);
  };
  const handleEvent = (type: string, data: API.IScoreReport) => {
    switch (type) {
      case 'detail':
        setModalData({
          isModalOpen: true,
          detail: data,
        });
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (isShow && schoolInfo?.code && semesterData?.currentSemester && month) {
      getGroups({
        enterpriseCode: schoolInfo.code,
        semester: semesterData.currentSemester,
        month,
      });
    }
  }, [isShow, schoolInfo?.code, semesterData?.currentSemester, month]);

  useEffect(() => {
    if (schoolInfo?.code && semesterData?.currentSemester) {
      // 先查询是否有有效的公示记录
      getPublication({ enterpriseCode: schoolInfo.code });
    }
    // 有则显示公示记录详情，没有则显示当前不在公示期
    // 查询公示信息需要先查分类，再按分类查询列表，列表可以利用getReports，界面不可编辑
  }, [schoolInfo?.code, semesterData?.currentSemester]);

  if (!isShow) {
    return (
      <div
        style={{
          paddingBlock: isMobile ? '16px' : '40px',
        }}
      >
        <NoData desc="暂无公示信息" color="rgb(25 ,137, 250)" src={pubilcImg} />
      </div>
    );
  }
  return (
    <div className={styles.publicRecordDetail}>
      <div
        style={{ fontSize: '18px', fontWeight: 'bold', marginBlock: '16px' }}
      >
        {new Date().getFullYear()}年{month}月份月度考核
      </div>
      <Row
        gutter={16}
        style={{
          marginBottom: '16px',
        }}
      >
        <Col span={isMobile ? 24 : undefined}>
          {isMobile ? (
            <Select
              style={{
                width: '100%',
                marginBottom: '16px',
              }}
              options={groups}
              value={role?.value}
              onChange={(value: string) => {
                setRole(groups.find((item) => item.value === value));
              }}
            />
          ) : (
            <Select
              className={styles.roleBread}
              options={groups}
              value={role?.value}
              onChange={(value: string) => {
                setRole(groups.find((item) => item.value === value));
              }}
            />
          )}
        </Col>
        <Col span={isMobile ? 24 : undefined}>
          <AutoLayout>
            <Search
              allowClear
              placeholder="搜索考核对象"
              onSearch={(value) => {
                setFilterText(value);
              }}
              style={{ width: isMobile ? '100%' : 300 }}
            />
          </AutoLayout>
        </Col>
      </Row>
      <ProTable<
        API.IScoreReport,
        {
          enterpriseCode: string | null;
          semester: string | null;
          month?: number | null;
          ruleId: number | null;
          assessedName?: string;
        }
      >
        bordered
        columns={getColumns({ pontColumns, handleEvent })}
        params={{
          enterpriseCode: schoolInfo?.code || null,
          semester: semesterData?.currentSemester || null,
          month,
          ruleId: role?.value ? Number(role?.value) : null,
          assessedName: filterText,
        }}
        request={async (query) => {
          const { enterpriseCode, semester, month, ruleId, assessedName } =
            query;
          console.log(query);
          if (!enterpriseCode || !semester || !month || !ruleId) {
            setPontColumns([]);
            return {
              success: false,
              data: [],
            };
          }
          const { errCode, data, msg } = await getReports(
            enterpriseCode,
            semester,
            month,
            ruleId,
            { assessedName },
          );
          if (errCode) {
            message.error(msg);
            setPontColumns([]);
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
          setPontColumns(data?.list?.[0]?.scoreReportDetail || []);
          return {
            data: data?.list || [],
            total: data?.list.length || 0,
            success: true,
          };
        }}
        rowKey="id"
        options={false}
        search={false}
        pagination={{
          pageSize: 10,
        }}
      />
      <ScoreInfo
        modalData={modalData}
        handleCancel={() => {
          setModalData({
            isModalOpen: false,
          });
        }}
      />
    </div>
  );
};

export default PublicRecord;

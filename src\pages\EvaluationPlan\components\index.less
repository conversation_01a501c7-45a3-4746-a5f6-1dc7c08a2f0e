.achievementContainer {
  .header {
    margin-bottom: 16px;

    .title {
      font-weight: 500;
      color: #333;

      .icon {
        margin-left: 8px;
        color: #999;
        cursor: pointer;
      }
    }
  }

  .gradeRow {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .gradeItem {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .label {
        margin-right: 4px;
        color: #666;
      }

      .typeText {
        margin: 0 4px;
      }

      .nameInput {
        width: 120px;
      }

      .typeSelect {
        width: 120px;
        margin-right: 8px;
      }

      .scoreInput {
        width: 80px;
      }

      .unit {
        margin: 0 8px;
      }

      .comparisonText {
        color: #666;
      }
    }

    .deleteButton {
      color: #999;

      &:hover {
        color: #ff4d4f;
      }
    }
  }

  .add {
    text-align: center;
  }
}

.createEvaluationRules {
  .header {
    margin-bottom: 16px;
    text-align: right;
  }

  .submitter {
    text-align: right;
  }

  .alert {
    margin-bottom: 16px;
  }

  :global {
    .ant-breadcrumb {
      color: #1677ff;
      cursor: pointer;
    }
  }
}

.checkboxContainer {
  margin-top: 5px;

  .checkboxBody {
    padding: 10px 0;
  }
}

.gradeAchievement {
  margin-left: 10px;
}

.observeList {
  width: 90%;
  padding: 5px 10px;
  margin: 0 auto;
}

.rulesList {
  .rulesItem {
    border: 1px solid #d9d9d9;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 8px;
    background-color: #f3f3f3;
  }
}

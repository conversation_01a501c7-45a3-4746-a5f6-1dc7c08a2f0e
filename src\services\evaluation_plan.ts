/** 考核方案管理 */
import { request } from '@umijs/max';

/** 查询列表  GET /assessment-plans */
export async function getAllEvaluationPlanAPI(params: {
  enterpriseCode?: string;
  semester?: string;
}) {
  return request<API.ResType<{ total?: number; list?: API.IAssessmentPlan[] }>>(
    '/assessment-plans',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /assessment-plan */
export async function createEvaluationPlanAPI(
  body: Partial<Omit<API.IAssessmentPlan, 'planId' | 'status'>> & {
    month: undefined;
    enterpriseCode: string;
    planStatus: undefined;
  },
) {
  return request<API.ResType>('/assessment-plans', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 修改  PUT /assessment-plan/:id */
export async function updateEvaluationPlanAPI(id: number, body: any) {
  return request<API.ResType<unknown>>(`/assessment-plans/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新方案状态 PUT /assessment-plan/:planId/status */
export async function updateEvaluationPlanStatusAPI(
  planId: number,
  body: { status: 'published' | 'draft' },
) {
  return request<API.ResType<unknown>>(`/assessment-plans/${planId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 5 * 60 * 1000,
    data: body,
  });
}

/** 删除  DELETE /assessment-plans/:planId */
export async function removeEvaluationPlanAPI(planId: number) {
  return request<API.ResType<unknown>>(`/assessment-plans/${planId}`, {
    method: 'DELETE',
  });
}

/**检查方案配置完整性 GET /assessment-plans/:planId/check */
export async function checkEvaluationPlanAPI(planId: number) {
  return request<API.ResType<unknown>>(`/assessment-plans/${planId}/check`, {
    method: 'GET',
  });
}

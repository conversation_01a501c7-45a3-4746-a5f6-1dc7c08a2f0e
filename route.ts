const routes: IBestAFSRoute[] = [
  {
    name: '认证失败',
    path: '/noAuth',
    component: './NoAuth',
    layout: false,
    hideInMenu: true,
  },
  {
    path: '/',
    redirect: '/fillEvaluation',
  },
  {
    name: '考核填报',
    path: '/fillEvaluation',
    hideChildrenInMenu: true,
    routes: [
      {
        path: '/fillEvaluation',
        redirect: '/fillEvaluation/list',
      },
      {
        path: '/fillEvaluation/list',
        name: '考核填报',
        component: './FillEvaluation',
      },
      {
        path: '/fillEvaluation/assessor',
        name: '考核人员',
        component: './FillEvaluation/Assessor',
      },
      {
        path: '/fillEvaluation/fillReport',
        name: '考核填报表',
        component: './FillEvaluation/FillReport',
      },
      {
        path: '/fillEvaluation/fillReport/detail',
        name: '考核填报详情',
        component: './FillEvaluation/FillReport/Detail',
      },
    ],
  },
  {
    name: '考核查询',
    path: '/queryEvaluation',
    hideChildrenInMenu: true,
    routes: [
      {
        path: '/queryEvaluation',
        redirect: '/queryEvaluation/list',
      },
      {
        path: '/queryEvaluation/list',
        name: '考核查询',
        component: './QueryEvaluation',
      },
      {
        path: '/queryEvaluation/personalList/detail',
        name: '个人查询',
        component: './QueryEvaluation/PersonalList/Detail',
      },
    ],
  },
  {
    name: '考核统计',
    path: '/statisticEvaluation',
    hideChildrenInMenu: true,
    routes: [
      {
        path: '/statisticEvaluation',
        redirect: '/statisticEvaluation/list',
      },
      {
        path: '/statisticEvaluation/list',
        name: '考核统计',
        component: './StatisticEvaluation',
      },
      {
        path: '/statisticEvaluation/statistic',
        name: '考核统计',
        component: './StatisticEvaluation/Statistic',
      },
      {
        path: '/statisticEvaluation/statistic/peopleList',
        name: '考核统计',
        component: './StatisticEvaluation/Statistic/PeopleList',
      },
      {
        path: '/statisticEvaluation/statistic/fillingSituation',
        name: '考核统计',
        component: './StatisticEvaluation/Statistic/FillingSituation',
      },
    ],
  },
  {
    name: '考核方案管理',
    path: '/evaluationPlan',
    component: './EvaluationPlan/List',
  },
];
export default routes;

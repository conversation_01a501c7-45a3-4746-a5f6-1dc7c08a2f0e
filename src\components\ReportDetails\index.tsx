import { assessmentTaskMonth } from '@/services/assessment_task';
import { ProCard } from '@ant-design/pro-components';
import { Alert, Col, message, Row, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

const ReportDetails: React.FC<{
  info: API.IScoreReport;
}> = ({ info }) => {
  const [dataList, setDataList] = useState<EvaluationDetail[]>([]);

  useEffect(() => {
    if (info) {
      (async () => {
        // 因为要显示每个人的打分信息，这里不能查报表详情，而应该从填报任务查起
        const { errCode, data, msg } = await assessmentTaskMonth({
          enterpriseCode: info!.enterpriseCode,
          semester: info!.semester,
          month: info!.month,
          // 这里写被考核人code
          assessedCode: info!.assessedCode,
        });
        if (errCode) {
          message.error(msg || '详情查询失败，请稍后再试');
          return;
        }
        const { list } = data || {};
        const newList: EvaluationDetail[] =
          list?.map((item) => {
            const list: ScoreDetail[] =
              item.scores?.map((val) => {
                return {
                  key: String(val.scoreId),
                  name: val.observationPoint?.pointName || '',
                  score: val.observationPoint?.baseScore || 0,
                  realScore: val.scoreValue || 0,
                };
              }) || [];
            return {
              key: String(item.taskId),
              role: `${item.scoringName}(${item.assessorName})`,
              score: list.reduce(
                (total, val) => total + Number(val.realScore || val.score),
                0,
              ),
              weight: item.scoring?.weight || 100,
              details: list,
            };
          }) || [];
        // 如果详情中有问卷，就把问卷也加进去
        const question = info!.scoreReportDetail?.find((val) => val.isQuestion);
        if (question) {
          newList.push({
            key: String(question.scoringId),
            role: question.scoringName,
            weight: question.weight || 100,
            isQuestion: true,
            score: Number(question.score),
          });
        }
        setDataList(newList);
      })();
    } else {
      setDataList([]);
    }
  }, [info]);

  return (
    <>
      <Alert
        message="当同一组有多人打分时，最终总分按平均值计算。"
        type="warning"
        closable
      />
      <Row gutter={[24, 24]} className={styles.detailGroup}>
        {dataList.map((item) => (
          <Col key={item.key} xs={24} sm={24} md={12} lg={12}>
            <ProCard
              title={item.role}
              extra={(item.score * item.weight) / 100}
              actions={
                item.isQuestion ? (
                  <div></div>
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      padding: '0 14px',
                    }}
                  >
                    <span style={{ fontWeight: 'bold' }}>
                      合计
                      <span
                        style={{ color: 'rgba(0,0,0,0.4)', fontSize: '12px' }}
                      >
                        （实际得分=总分*{item.weight}%）
                      </span>
                    </span>
                    <span style={{ color: '#0ea514', fontWeight: 'bold' }}>
                      {item.score}
                    </span>
                  </div>
                )
              }
              boxShadow
            >
              {item.details?.map((val) => (
                <div className={styles.scoreLine} key={val.key}>
                  <div>
                    {val.name}
                    <span>（基础分{val.score}分）</span>
                  </div>
                  <div>
                    {val.realScore || (
                      <Tooltip title="未打分暂按基础分计分">
                        <span
                          style={{
                            color: '#aaa',
                            fontWeight: 'normal',
                            fontSize: '12px',
                          }}
                        >
                          未打分
                        </span>
                      </Tooltip>
                    )}
                  </div>
                </div>
              ))}
              {!!item.isQuestion && (
                <div
                  style={{
                    display: 'flex',
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#aaa',
                    fontWeight: 'bold',
                    fontSize: '20px',
                  }}
                >
                  问卷没有详情
                </div>
              )}
            </ProCard>
          </Col>
        ))}
      </Row>
    </>
  );
};

export default ReportDetails;

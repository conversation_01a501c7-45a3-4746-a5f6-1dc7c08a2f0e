import { getSchoolRoles, getSchoolUsers } from '@/services/utils';
import {
  ModalForm,
  ModalFormProps,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Col, message, Row, Space, Tag, Transfer } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

interface AddScoringModalType extends ModalFormProps {
  onCancel: () => void;
  data?: API.IScoring;
  onSubmit?: (value: any) => Promise<boolean | undefined>;
  isReadonly: boolean;
}

const AddScoringModal: React.FC<AddScoringModalType> = ({
  onCancel,
  data,
  onSubmit,
  isReadonly,
  ...restProps
}) => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const formRef = useRef<ProFormInstance>();
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [transferData, setTransferData] = useState<any[]>([]);

  const handleSubmit = async (values: any) => {
    try {
      if (onSubmit) {
        if (values?.assessorType === 'user' && !values?.users?.length) {
          return message.warning('参评人员不能为空');
        }
        const result = await onSubmit({
          ...values,
          rolesTypes: undefined,
          roles: values?.assessorType === 'role' ? values?.roles : undefined,
          users: values?.assessorType === 'user' ? values?.users : undefined,
        });

        // 提交成功后重置状态
        if (result) {
          setTargetKeys([]);
        }
        return result;
      }
      return true;
    } catch (error) {
      console.error('提交失败: ', error);
      message.error('提交失败' + error);
      return false;
    }
  };

  const handleChange = (newTargetKeys: React.Key[]) => {
    setTargetKeys(newTargetKeys as string[]);
    const selectedItems = transferData.filter((item) =>
      newTargetKeys.includes(item.key),
    );

    formRef.current?.setFieldsValue({
      users: selectedItems.map((item) => ({
        userCode: item.key,
        userName: item.title,
      })),
    });
  };

  useEffect(() => {
    if (data && restProps.open) {
      const initialKeys = data.users?.map((user) => user.userCode) || [];
      setTargetKeys(initialKeys);
      if (data?.assessorType === 'user') {
        formRef.current?.setFieldsValue({
          users: data.users || [],
        });
      }
      setTimeout(() => {
        formRef.current?.setFieldsValue({
          ...data,
          rolesTypes: data?.roles?.map((item: any) => item.roleCode) || [],
        });
      }, 0);
    }
  }, [data, restProps.open]);

  /** 获取用户列表 */
  const fetchUserList = async () => {
    const { errCode, data, msg } = await getSchoolUsers({
      enterpriseCode: schoolInfo?.code || '',
    });
    if (errCode) {
      message.warning('获取用户列表失败，' + msg);
      setTransferData([]);
    } else {
      setTransferData(
        (data?.list ?? []).map((item: any) => ({
          title: item.name,
          description: item.code,
          key: item.code,
        })),
      );
    }
  };

  useEffect(() => {
    fetchUserList();
  }, []);

  return (
    <>
      <ModalForm
        width={650}
        formRef={formRef}
        title={data ? '编辑赋分规则' : '新增赋分规则'}
        {...restProps}
        layout="horizontal"
        onFinish={handleSubmit}
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
          onCancel: () => {
            setTargetKeys([]);
            onCancel();
          },
          styles: {
            body: {
              marginTop: '20px',
              maxHeight: '500px',
              overflowY: 'auto',
              overflowX: 'hidden',
              paddingRight: '5px',
            },
          },
        }}
      >
        <ProFormText name="scoringId" hidden />
        <ProFormText name="roles" hidden />
        <ProFormText name="users" hidden />
        <ProFormText
          name="title"
          label="规则名称"
          rules={[{ required: true, message: '请输入规则名称' }]}
        />
        <Row gutter={16}>
          <Col span={12}>
            <ProFormRadio.Group
              name="assessorType"
              readonly={isReadonly}
              initialValue="role"
              label="考评类型"
              options={[
                { label: '身份', value: 'role' },
                { label: '个人', value: 'user' },
                { label: '问卷', value: 'question' },
              ]}
              fieldProps={{
                onChange: (e) => {
                  if (e.target.value === 'question') {
                    formRef.current?.setFieldsValue({
                      rolesTypes: undefined,
                      users: undefined,
                    });
                  }
                },
              }}
              rules={[{ required: true, message: '请选择参评类型' }]}
            />
          </Col>
          <Col span={12}>
            <ProFormDigit
              label="权重"
              name="weight"
              min={0}
              max={100}
              initialValue={100}
              fieldProps={{ precision: 0, suffix: '%' }}
              WS
              rules={[{ required: true, message: '请输入权重' }]}
            />
          </Col>
        </Row>
        <ProFormDependency name={['assessorType']}>
          {({ assessorType }) => {
            if (!assessorType) return null;
            return (
              <>
                {assessorType === 'role' && (
                  <ProFormSelect
                    readonly={isReadonly}
                    name="rolesTypes"
                    label="身份类型"
                    mode="multiple"
                    showSearch
                    request={async () => {
                      const { errCode, data, msg } = await getSchoolRoles({
                        enterpriseCode: schoolInfo?.code || '',
                      });
                      if (errCode) {
                        message.warning('获取身份列表失败，' + msg);
                        return [];
                      }
                      return (data?.list ?? []).map((item: any) => ({
                        label: item.name,
                        value: item.code,
                      }));
                    }}
                    onChange={(_value, options) => {
                      const newData = options.map(
                        (item: { title: string; value: any }) => {
                          return {
                            roleName: item.title,
                            roleCode: item.value,
                          };
                        },
                      );
                      formRef.current?.setFieldsValue({
                        roles: newData,
                      });
                    }}
                    rules={[
                      { required: true, message: '请至少选择一个身份类型' },
                    ]}
                  />
                )}
                {assessorType === 'user' && (
                  <>
                    {isReadonly ? (
                      <div className={styles.userListBox}>
                        <label className={styles.userListLabel}>
                          已选考评人：
                        </label>
                        <div className={styles.userList}>
                          {(data?.users ?? []).map(
                            (item: any, index: number) => {
                              return (
                                <Space key={index}>
                                  <Tag>{item.userName}</Tag>
                                </Space>
                              );
                            },
                          )}
                        </div>
                      </div>
                    ) : (
                      <Transfer
                        dataSource={transferData}
                        targetKeys={targetKeys}
                        onChange={handleChange}
                        titles={['待选人员', '已选人员']}
                        render={(item) => `${item.title} (${item.description})`}
                        listStyle={{ width: '100%', height: 300 }}
                        showSearch
                      />
                    )}
                  </>
                )}
              </>
            );
          }}
        </ProFormDependency>
      </ModalForm>
    </>
  );
};
export default AddScoringModal;

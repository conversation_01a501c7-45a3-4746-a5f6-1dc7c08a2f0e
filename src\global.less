body {
  // 全局变量在此定义
  --primary-color: #2f51ff;
  --primary-color-1: rgba(47, 81, 255, 10%);
  --primary-color-2: rgba(47, 81, 255, 20%);
  --primary-color-5: rgba(47, 81, 255, 50%);
  --primary-color-8: rgba(47, 81, 255, 80%);
  --border-radius-base: 8px; // 组件/浮层圆角
  --title-color: #333; //  title字体颜色
  --sub-color: #666; //  subTitle 颜色
  --tip-color: #888; // tip 颜色
  --card-bg: #fff; // 面板 背景 颜色
  --card-shadow: 0 0 8px rgba(203, 203, 203, 20%);
  --font-size: 14px;

  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  canvas {
    display: block;
  }

  ul,
  ol {
    list-style: none;
  }

  .colorWeak {
    filter: invert(80%);
  }

  .lightColor {
    line-height: 35px;
    color: #6b7280;
    margin: 0;
  }

  a {
    color: var(--primary-color);
  }

  .card-wrapper {
    padding: 24px;
    background: var(--card-bg);
    border-radius: var(--border-radius-base);
    box-shadow: var(--card-shadow);
  }

  .ant-layout {
    min-height: 100vh;

    .ant-layout-sider {
      .ant-menu {
        .ant-menu-item-selected {
          color: var(--primary-color);
          background-color: var(--primary-color-1);
        }
      }
    }

    .ant-breadcrumb {
      a {
        color: var(--primary-color-8);
      }
    }

    main.ant-pro-layout-content-has-page-container {
      height: calc(100vh - 56px);
      padding: 24px;

      .ant-page-header-heading {
        padding: 0;

        .ant-page-header-heading-left {
          margin: 0;

          .ant-page-header-back {
            & + .ant-page-header-heading-title {
              visibility: hidden;
            }

            .ant-page-header-back-button,
            .anticon {
              font-weight: normal;
              color: var(--primary-color);
              font-size: 14px;
              line-height: 26px;
              cursor: pointer;
              margin-right: 6px;
            }
          }
        }
      }

      .ant-pro-page-container {
        .card-wrapper;

        padding-top: 8px;
        height: 100%;
        overflow-y: auto;

        .ant-page-header,
        .ant-breadcrumb,
        .ant-pro-table .ant-pro-card-body,
        .ant-pro-page-container-children-container {
          padding: 0;
        }
      }
    }

    main.ant-pro-layout-has-header {
      height: calc(100vh - 56px);
      padding: 24px;
    }
  }

  .ant-picker {
    width: 100%;
  }

  .ant-tree {
    .ant-tree-node-content-wrapper.ant-tree-node-selected,
    .ant-tree-checkbox + span.ant-tree-node-selected {
      background-color: var(--primary-color-2);
    }
  }
}

@media (max-width: 768px) {
  .ant-pro-page-container-children-container-no-header,
  .ant-pro-page-container .ant-pro-page-container-warp-page-header {
    padding: 16px;
  }

  .ant-breadcrumb {
    padding: 16px 16px 0;

    a {
      color: var(--primary-color);
    }
  }

  .ant-page-header-back {
    color: var(--primary-color);
  }

  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

.delete {
  color: #ff4d4f;
  cursor: pointer;
}

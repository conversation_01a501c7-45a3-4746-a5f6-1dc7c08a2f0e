// 运行时配置
import logoImg from '@/assets/logo.png';
import {
  RequestConfig,
  RequestOptions,
  RunTimeLayoutConfig,
  history,
} from '@umijs/max';
import { dataConverLogin, decodeParams } from './services/sso';
import { getAuthCookie, getQueryObj } from './utils/calc';
// const env_screen = envjudge();
// const isMobile = env_screen.includes('mobile');

// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<InitialState> {
  let currentUser;
  let schoolInfo;
  const { encryptedAuthStr, value, auth } = getQueryObj();
  if (value || encryptedAuthStr) {
    const res = await decodeParams('getDataConver', encryptedAuthStr || value);
    const { errCode, data } = res;
    if (errCode) {
      // TODO 跳转错误页
      history.push('/noAuth');
    } else {
      const { userCode, enterpriseCode } = data || {};
      if (enterpriseCode && userCode) {
        const { errCode: errCode1, data: userInfo } = await dataConverLogin({
          userCode: userCode,
          enterpriseCode,
        });
        if (errCode1) {
          // TODO 跳转错误页
          history.push('/noAuth');
        } else {
          sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
          currentUser = {
            mobile: userInfo?.mobile,
            userCode: userInfo?.work_code,
            realName: userInfo?.realName,
            username: userInfo?.username,
          };
          schoolInfo = {
            id: userInfo?.enterprise?.id,
            code: userInfo?.enterprise?.code,
            name: userInfo?.enterprise?.name,
          };
          let href = location.pathname;
          if (auth) {
            href = href + '?auth=' + auth;
          }
          window.history.replaceState('', '', href);
          // setTimeout(()=>{
          //   history.replace(location.pathname)
          // },200)
        }
      } else {
        history.push('/noAuth');
      }
    }
  } else if (sessionStorage.getItem('userInfo')) {
    const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}');
    currentUser = {
      mobile: userInfo?.mobile,
      userCode: userInfo?.work_code,
      realName: userInfo?.realName,
      username: userInfo?.username,
    };
    schoolInfo = {
      id: userInfo?.enterprise?.id,
      code: userInfo?.enterprise?.code,
      name: userInfo?.enterprise?.name,
    };
  } else {
    history.push('/noAuth');
  }
  return {
    currentUser,
    schoolInfo,
  };
}

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    logo: logoImg,
    menu: {
      locale: false,
    },
    // pure: true,
    layout: 'mix',
    // menuRender: false,
    menuFooterRender: () => {
      return (
        <div style={{ textAlign: 'center' }}>
          {initialState?.schoolInfo?.name}
        </div>
      );
    },
    contentWidth: 'Fluid',
    fixedHeader: true,
    fixSiderbar: true,
  };
};

export const request: RequestConfig = {
  baseURL: '/api_evaluate',
  timeout: 30000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  errorConfig: {
    errorHandler() {},
    errorThrower() {},
  },
  requestInterceptors: [
    (config: RequestOptions) => {
      config.headers = {
        'x-csrf-token': getAuthCookie(),
        ...config.headers,
      };
      // 拦截请求配置，进行个性化处理。
      return { ...config };
    },
  ],
  responseInterceptors: [],
};

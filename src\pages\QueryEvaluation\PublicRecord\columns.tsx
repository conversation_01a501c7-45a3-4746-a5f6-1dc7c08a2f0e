import { getQueryObj } from '@/utils/calc';
import { ProColumns } from '@ant-design/pro-components';
import { Button } from 'antd';
const { auth } = getQueryObj();

export const getPoints = (ponts: API.IScoreReportDetail[]): ProColumns[] => {
  return ponts.map((p, i) => ({
    title: `${p.scoringName}`,
    dataIndex: ['scoreReportDeatil', `${i}`],
    align: 'center',
    render: (_, entity: API.IScoreReport) => {
      const score =
        (entity.scoreReportDetail || []).find(
          (sr) => sr.scoringId === p.scoringId,
        )?.score || 0;
      return Number(score);
    },
  }));
};
const getColumns = ({
  pontColumns = [],
  handleEvent,
}: {
  pontColumns?: API.IScoreReportDetail[];
  handleEvent: (type: string, data: API.IScoreReport) => void;
}): ProColumns<API.IScoreReport, 'text'>[] => [
  {
    title: '序号',
    dataIndex: 'index',
    valueType: 'index',
    align: 'center',
    width: 60,
  },
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'assessedName',
    width: 120,
  },
  {
    title: '观测点',
    align: 'center',
    children: getPoints(pontColumns),
  },
  {
    title: '总分',
    align: 'center',
    dataIndex: 'score',
    valueType: 'digit',
    width: 120,
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'score',
    hidden: !!(auth !== 'admin'),
    width: 120,
    render: (_, record) => {
      return (
        <Button
          type="link"
          onClick={() => {
            handleEvent('detail', record);
          }}
        >
          得分详情
        </Button>
      );
    },
  },
];
export default getColumns;

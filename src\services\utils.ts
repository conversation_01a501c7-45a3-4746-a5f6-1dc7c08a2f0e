import { request } from '@umijs/max';

/** 获取学校学年学期列表  获取学校学年学期列表  GET /api/school/semesters */
export const getAllSemesterList = ({
  enterpriseCode,
}: {
  /** 企业编码 */
  enterpriseCode?: string;
}) => {
  return request<API.ResType<{ total: number; list: any[] }>>(
    `/schoolInfo/${enterpriseCode}/semester`,
    {
      method: 'GET',
    },
  );
};

/** GET  查询指定学校的角色列表  /schoolInfo/{enterpriseCode}/schoolRoles */
export const getSchoolRoles = ({
  enterpriseCode,
}: {
  /** 企业编码 */
  enterpriseCode?: string;
}) => {
  return request<API.ResType<{ total: number; list: any[] }>>(
    `/schoolInfo/${enterpriseCode}/schoolRoles`,
    {
      method: 'GET',
    },
  );
};

/** GET  获取指定学校下的所有成员 /schoolInfo/{enterpriseCode}/users */
export const getSchoolUsers = ({
  enterpriseCode,
}: {
  /** 企业编码 */
  enterpriseCode?: string;
}) => {
  return request<API.ResType<{ total: number; list: any[] }>>(
    `/schoolInfo/${enterpriseCode}/users`,
    {
      method: 'GET',
    },
  );
};

/** 文件删除  文件删除  DELETE /upload/destory/${param0} */
export async function controllerUploadReplace( // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    id: string;
  },
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/upload/destory/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
    baseURL: '/edu_api',
  });
}

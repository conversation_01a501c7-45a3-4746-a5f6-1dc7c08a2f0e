import { editQuestionScore, getReports_question } from '@/services/report';
import { ActionType, EditableProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const QuestionScoreManager: React.FC<{
  enterpriseCode: string;
  semester: string;
  month: number;
  ruleId: number;
}> = ({ enterpriseCode, semester, month, ruleId }) => {
  const actionRef = useRef<ActionType>();
  const getList = async () => {
    const { errCode, data, msg } = await getReports_question(
      enterpriseCode,
      semester,
      month,
      ruleId,
    );
    if (errCode) {
      message.error(msg);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
    return {
      data: data?.list || [],
      total: data?.list.length || 0,
      success: true,
    };
  };
  return (
    <EditableProTable<API.IScoreReportDetail>
      actionRef={actionRef}
      rowKey="detailId"
      columns={[
        {
          title: '姓名',
          dataIndex: ['scoreReport', 'assessedName'],
          readonly: true,
        },
        {
          title: '工号',
          dataIndex: ['scoreReport', 'assessedCode'],
          readonly: true,
        },
        {
          title: '得分',
          dataIndex: 'score',
          valueType: 'digit',
        },
        {
          title: '操作',
          valueType: 'option',
          width: 200,
          render: (_text, record, _, action) => [
            <a
              key="editable"
              onClick={() => {
                action?.startEditable?.(record.detailId);
              }}
            >
              编辑
            </a>,
          ],
        },
      ]}
      recordCreatorProps={false}
      editable={{
        onSave: async (_key, record) => {
          const { detailId, score } = record;
          if (typeof score !== 'number' || score < 0 || score > 100) {
            message.error('得分必须在0-100之间');
          } else {
            const { errCode, msg } = await editQuestionScore(detailId, score);
            if (errCode) {
              message.error(msg);
            } else {
              message.success('保存成功');
            }
          }
          actionRef.current?.reload();
        },
        actionRender: (_row, _config, dom) => [dom.save, dom.cancel],
      }}
      request={getList}
    />
  );
};

export default QuestionScoreManager;

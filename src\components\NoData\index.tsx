/*
 * @description:
 * @author: wuzhan
 * @Date: 2022-11-18 16:52:16
 * @LastEditTime: 2022-11-23 15:13:54
 * @LastEditors: wuzhan
 */

import type { FC, ReactNode } from 'react';
import styles from './index.less';

type propttype = {
  imgWidth?: string;
  src?: string;
  color?: string;
  desc?: ReactNode | string;
};

const NoData: FC<propttype> = ({ imgWidth, src, color, desc }) => {
  return (
    <div className={styles.noData}>
      <img
        src={
          src ||
          'https://ysp-uploader-1301720845.cos.ap-nanjing.myqcloud.com/score-analysis/empty1.png'
        }
        style={{
          margin: '24px auto',
          width: imgWidth ? `${imgWidth}` : '100px',
          display: 'block',
        }}
      />
      <p
        style={{
          // lineHeight: '40px',
          textAlign: 'center',
          color: color || 'rgba(40, 97, 255, 0.6)',
          fontSize: '14px',
        }}
      >
        {desc}
      </p>
    </div>
  );
};

export default NoData;

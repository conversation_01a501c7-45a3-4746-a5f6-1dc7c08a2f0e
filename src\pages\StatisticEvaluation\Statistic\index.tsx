import {
  getAssessmentTaskAnalysis,
  getAssessmentTaskGroups,
} from '@/services/assessment_task';
import { getQueryParams } from '@/utils/calc';
import {
  InfoCircleOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Link, useModel } from '@umijs/max';
import { Col, Flex, message, Row, Space, Spin, Tooltip } from 'antd';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import ChartPie from './ChartPie';
import styles from './index.less';

const Statistic = () => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const { month, semester, year, status } = getQueryParams();
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState<any[]>([]);

  /** 获取填写进度 */
  const getProgress = async (ruleId: number, index: number) => {
    const { errCode, data, msg } = await getAssessmentTaskAnalysis({
      enterpriseCode: schoolInfo?.code || '',
      semester: semester || '',
      month: month || '',
      ruleId,
    });

    if (errCode) {
      message.warning('获取填写进度失败，' + msg);
      return;
    }

    setListData((prev) => {
      const newData = [...prev];
      newData[index] = {
        ...newData[index],
        info: [
          { name: '考核任务总数', value: data?.total ?? 0 },
          { name: '已完成考核', value: data?.completed ?? 0 },
          { name: '未完成考核', value: data?.pending ?? 0 },
          {
            name: '考核完成率',
            value: data?.total
              ? `${((data.completed / data.total) * 100).toFixed(2)}%`
              : '0%',
          },
        ],
      };
      return newData;
    });
  };

  /** 获取数据 */
  const getData = async () => {
    if (!schoolInfo?.code || !semester || !month) {
      return;
    }
    setLoading(true);
    const { errCode, data, msg } = await getAssessmentTaskGroups({
      enterpriseCode: schoolInfo.code,
      semester: semester,
      month: Number(month),
    });

    if (errCode) {
      message.warning('获取考核分组列表失败，' + msg);
      setLoading(false);
      return;
    }
    console.log(status);

    if (data?.list && data.list.length > 0) {
      const newListData = data.list.map((group: any, index: number) => ({
        title: group.ruleName || listData[index]?.ruleName || '未知分组',
        link: [
          {
            name: '查看进度',
            link: `/statisticEvaluation/statistic/fillingSituation?ruleId=${group.ruleId}&ruleName=${group.ruleName}`,
            disabled: false,
          },
          {
            name: '查看详情',
            link: `/statisticEvaluation/statistic/peopleList?ruleId=${group.ruleId}&ruleName=${group.ruleName}`,
            disabled: status !== '已公示',
          },
        ],
        info: listData[index]?.info || [],
        explain: listData[index]?.explain || [],
      }));

      setListData(newListData);

      // 为每个规则获取进度
      data.list.forEach((group: any, index: number) => {
        if (group.ruleId) {
          getProgress(group.ruleId, index);
        }
      });
    }

    setLoading(false);
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <PageContainer
      ghost
      header={{
        title: '返回上一页',
        breadcrumb: {},
        backIcon: (
          <>
            <LeftOutlined />
            返回上一页
          </>
        ),
        onBack: () => history.back(),
      }}
    >
      <header className={styles.statisticHeader}>
        <div>
          {year} 年 {month} 月月度考核填报进度 - {schoolInfo?.name}
        </div>
      </header>
      <div className={classNames('card-wrapper', styles.statisticWrapper)}>
        {listData.map((item) => {
          const link = `&month=${month}&year=${year}&semester=${semester}`;
          return (
            <div className={styles.statisticCard} key={item.title}>
              <div className={styles.statisticCardTitle}>
                <div>
                  {item?.title}
                  <Tooltip title="注意：一个人可能有多个填写任务，为了统计更准确，统一以单个任务为统计单元">
                    <InfoCircleOutlined style={{ marginLeft: 5 }} />
                  </Tooltip>
                </div>
                <Space size="large">
                  {item.link.map(
                    (item: { disabled: any; link: any; name: string }) => {
                      if (item.disabled) {
                        return null;
                      }
                      return (
                        <Link to={`${item.link}${link}`} key={item.name}>
                          {item.name}
                          <RightOutlined />
                        </Link>
                      );
                    },
                  )}
                </Space>
              </div>
              <div className={styles.statisticCardContent}>
                <Row>
                  <Col span={12}>
                    <Flex wrap gap={'16px'}>
                      {item.info.map(
                        (item: { name: string; value: number }) => {
                          return (
                            <div className={styles.subCard} key={item.name}>
                              <div>
                                <div className={styles.subCardTitle}>
                                  {item.name}
                                </div>
                                <div>
                                  {loading ? (
                                    <span style={{ fontSize: 12 }}>
                                      数据加载中...
                                    </span>
                                  ) : (
                                    item.value
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        },
                      )}
                    </Flex>
                  </Col>
                  <Col span={12}>
                    <div className={styles.chartPie}>
                      <p>考核进度分布</p>
                      <Spin spinning={loading} tip="数据加载中...">
                        <div>
                          <ChartPie
                            data={[
                              {
                                type: '已完成',
                                value: Number(item.info[1]?.value),
                              },
                              {
                                type: '未完成',
                                value: Number(item.info[2]?.value),
                              },
                            ]}
                          />
                        </div>
                      </Spin>
                    </div>
                  </Col>
                  {/* <Col span={8}>
                    <ProCard
                      bordered
                      title="考核得分说明"
                      className={styles.scoreExplain}
                    >
                      <div
                        style={{
                          width: '100%',
                        }}
                      >
                        <p>{item?.title}考核得分：</p>
                        <ul>
                          {item.explain?.map((item, index) => {
                            return (
                              <li key={index} title={item}>
                                {item}
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    </ProCard>
                  </Col> */}
                </Row>
              </div>
            </div>
          );
        })}
      </div>
    </PageContainer>
  );
};

export default Statistic;

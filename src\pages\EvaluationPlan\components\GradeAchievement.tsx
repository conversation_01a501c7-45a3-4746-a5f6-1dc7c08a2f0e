import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Input, InputNumber, Select } from 'antd';
import classNames from 'classnames';
import { useMemo } from 'react';
import styles from './index.less';

// 完善类型定义
// 定义比较类型
type ComparisonType = '大于等于' | '大于' | '小于等于' | '小于' | '等于';

// 完善Grade接口
interface Grade {
  name: string;
  score: number | '';
  type: ComparisonType;
}

// 完善组件Props类型
interface GradeAchievementProps {
  /** 当前等次配置数组 */
  value?: Grade[];
  /** 等次配置变化回调 */
  onChange?: (grades: Grade[]) => void;
  /** 是否只读模式 */
  readonly?: boolean;
  /** 自定义类名 */
  className?: string;
}

const GradeAchievement: React.FC<GradeAchievementProps> = ({
  value = [],
  onChange,
  className,
  readonly,
}) => {
  // 使用默认值和外部传入值的合并
  const defaultGrades = [
    { name: '优秀', score: 85, type: '大于等于' as ComparisonType },
    { name: '良好', score: 70, type: '大于等于' as ComparisonType },
    { name: '合格', score: 60, type: '大于等于' as ComparisonType },
    { name: '不合格', score: 0, type: '大于等于' as ComparisonType },
  ];

  // 确保使用有效的值
  const grades =
    Array.isArray(value) && value.length > 0 ? value : defaultGrades;

  // 创建比较符号映射
  const comparisonMap = useMemo(
    () => ({
      大于等于: '≥',
      大于: '>',
      小于等于: '≤',
      小于: '<',
      等于: '=',
    }),
    [],
  );

  // 修改处理函数，不再过滤不完整的项
  const handleScoreChange = (index: number, value: string | number | null) => {
    // 处理 InputNumber 可能返回的 null 值
    const numValue = value === null || value === '' ? '' : Number(value);
    if (numValue !== '' && (isNaN(numValue) || numValue < 0)) return;

    const newGrades = [...grades];
    newGrades[index] = { ...newGrades[index], score: numValue };
    onChange?.(newGrades);
  };

  const handleNameChange = (index: number, value: string) => {
    const newGrades = [...grades];
    newGrades[index] = { ...newGrades[index], name: value };
    onChange?.(newGrades);
  };

  const handleTypeChange = (index: number, value: ComparisonType) => {
    const newGrades = [...grades];
    newGrades[index] = { ...newGrades[index], type: value };
    onChange?.(newGrades);
  };

  // 添加最大数量限制
  const MAX_GRADES = 10;
  const addGrade = () => {
    if (grades.length >= MAX_GRADES) return;
    onChange?.([...grades, { name: '', score: '', type: '大于等于' }]);
  };

  const removeGrade = (index: number) => {
    if (grades.length <= 1) return;
    const newGrades = [...grades];
    newGrades.splice(index, 1);
    onChange?.(newGrades);
  };

  // 优化比较文本生成
  const getComparisonText = (index: number) => {
    if (index === 0 || index >= grades.length) return null;

    const prevGrade = grades[index - 1];
    const currentGrade = grades[index];

    if (
      !prevGrade ||
      !currentGrade ||
      prevGrade.score === '' ||
      currentGrade.score === ''
    ) {
      return null;
    }

    return `且 < ${prevGrade.score}分`;
  };

  return (
    <div className={classNames(styles.achievementContainer, className)}>
      {grades.map((grade, index) => (
        <div className={styles.gradeRow} key={index}>
          <div className={styles.gradeItem}>
            <span className={styles.label}>名称：</span>
            {readonly ? (
              grade.name
            ) : (
              <Input
                value={grade.name}
                onChange={(e) => handleNameChange(index, e.target.value)}
                placeholder="请输入名称"
                className={styles.nameInput}
              />
            )}
          </div>

          <div className={styles.gradeItem}>
            <span className={styles.label}>分数 {readonly ? null : '：'}</span>
            {readonly ? (
              <span className={styles.typeText}>
                {comparisonMap[grade.type]}
              </span>
            ) : (
              <Select
                value={grade.type}
                onChange={(value) => handleTypeChange(index, value)}
                className={styles.typeSelect}
                options={[
                  { value: '大于等于', label: '大于等于' },
                  { value: '大于', label: '大于' },
                  { value: '小于等于', label: '小于等于' },
                  { value: '小于', label: '小于' },
                  { value: '等于', label: '等于' },
                ]}
              />
            )}
            {readonly ? (
              grade.score === 0 ? (
                '0'
              ) : (
                grade.score
              )
            ) : (
              <InputNumber
                min={0}
                value={grade.score === 0 ? 0 : grade.score}
                onChange={(value) => handleScoreChange(index, value)}
                placeholder="请输入"
                className={styles.scoreInput}
              />
            )}
            <span className={styles.unit}>分</span>

            {getComparisonText(index) && (
              <span className={styles.comparisonText}>
                {getComparisonText(index)}
              </span>
            )}
          </div>

          {!readonly && grades.length > 1 && (
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => removeGrade(index)}
              className={styles.deleteButton}
            />
          )}
        </div>
      ))}

      <div className={styles.add}>
        {!readonly && grades.length < MAX_GRADES && (
          <Button type="link" icon={<PlusOutlined />} onClick={addGrade}>
            新增
          </Button>
        )}
      </div>
    </div>
  );
};

export default GradeAchievement;

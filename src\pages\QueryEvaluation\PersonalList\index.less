.personalList {
  .searchWrapper {
    margin-bottom: 16px;
  }

  .cardBg {
    background: rgba(34, 197, 94, 100%);
    padding: 0 0 0 4px;
    border-radius: 10px;

    .peopleCard {
      border: 1px solid #e5e7eb;
      box-shadow: 0 2px 4px 0 #98989826;
      padding: 16px;

      .scoreCon {
        height: 100px;
        font-weight: bold;
        background: linear-gradient(
          to right,
          rgba(34, 197, 94, 10%),
          rgba(34, 197, 94, 0%)
        );
        border-radius: 10px;
        margin-block: 5px;

        .hasScore {
          color: #0ea514;

          .score {
            font-size: 40px;

            & + span {
              font-size: 18px;
              margin-left: 8px;
            }
          }
        }

        .noScore {
          color: #989898;
          font-size: 24px;
        }
      }
    }
  }
}

.personalListDetail {
  .detailWrapper {
    .detailTitle {
      line-height: 40px;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    :global {
      .ant-table-container {
        .ant-table-thead {
          padding: 4px 0;

          .ant-table-cell {
            padding: 0 8px !important;
            line-height: 35px;
            background-color: #efefef;
          }
        }

        .ant-table-cell {
          padding: 0 8px !important;
          line-height: 35px;
        }

        table,
        thead,
        tbody,
        tr,
        td,
        th {
          border-color: #dadada !important;
        }
      }
    }
  }
}

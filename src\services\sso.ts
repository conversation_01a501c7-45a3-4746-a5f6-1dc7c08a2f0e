// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

export async function decodeParams(
  apiCode: string,
  value?: string,
  options?: { [key: string]: any },
) {
  return request<any>('/apis/request', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    baseURL: '/api_common',
    data: {
      apiCode,
      query: { value },
    },
    ...(options || {}),
  });
}

/** 登录 POST /data_conver_login */
export async function dataConverLogin(
  body: {
    userCode?: string;
    enterpriseCode?: string;
    globalSessionToken?: string;
  },
  options?: { [key: string]: any },
) {
  return request<any>('/auth/data_conver_login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

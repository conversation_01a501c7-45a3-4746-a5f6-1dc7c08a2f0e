/**
 * 基础实体接口
 */
export interface BaseEntity {
  /** 创建时间 - 记录实体创建的日期时间 */
  createdAt?: Date;
  /** 更新时间 - 记录实体最后一次更新的日期时间 */
  updatedAt?: Date;
}

/**
 * 考核方案接口
 */
export interface IAssessmentPlan extends BaseEntity {
  /** 方案ID - 考核方案的唯一标识符 */
  planId: number;
  /** 方案名称 - 考核方案的名称 */
  planName: string;
  /** 学期 - 考核方案所属的学期 */
  semester: string;
  /** 开始月份 - 考核方案的开始日期 */
  startMonth: Date;
  /** 结束月份 - 考核方案的结束日期 */
  endMonth: Date;
}

/**
 * 赋分形式配置接口
 */
export interface IScoringConfig extends BaseEntity {
  /** 配置ID - 赋分配置的唯一标识符 */
  configId: number;
  /** 方案ID - 关联的考核方案ID */
  planId: number;
  /** 评分类型 - 分数制或星级制 */
  scoringType: 'score' | 'star';
  /** 显示类型 - 分数显示方式，可选分数或等级 */
  displayType?: 'score' | 'grade';
  /** 等级规则 - 分数到等级的映射规则 */
  gradeRules?: Record<string, string>;
  /** 最大调整值 - 评分的最大上调幅度 */
  maxAdjustment?: number;
  /** 最小调整值 - 评分的最大下调幅度 */
  minAdjustment?: number;
  /** 最大星级数 - 星级制评分的最大星星数量 */
  maxStars?: number;
}

/**
 * 考核规则接口
 */
export interface IAssessmentRule extends BaseEntity {
  /** 规则ID - 考核规则的唯一标识符 */
  ruleId: number;
  /** 方案ID - 关联的考核方案ID */
  planId: number;
  /** 配置ID - 关联的赋分配置ID */
  configId: number;
  /** 规则要点 - 考核规则的具体内容 */
  rulePoint: string;
  /** 权重 - 该规则在整体评分中的权重 */
  weight: number;
  /** 基础分值 - 该规则的基础分数 */
  baseScore?: number;
}

/**
 * 考核任务接口
 */
export interface IAssessmentTask extends BaseEntity {
  /** 任务ID - 考核任务的唯一标识符 */
  taskId: number;
  /** 方案ID - 关联的考核方案ID */
  planId: number;
  /** 评估者用户ID - 执行评估的用户ID */
  assessorUserid: string;
  /** 被评估者用户ID - 被评估的用户ID */
  assessedUserid: string;
  /** 月份 - 考核任务所属的月份 */
  month: Date;
  /** 状态 - 考核任务的当前状态 */
  status: 'pending' | 'completed';
}

/**
 * 评分接口
 */
export interface IScore extends BaseEntity {
  /** 评分ID - 评分记录的唯一标识符 */
  scoreId: number;
  /** 任务ID - 关联的考核任务ID */
  taskId: number;
  /** 规则ID - 关联的考核规则ID */
  ruleId: number;
  /** 分值 - 具体的评分值 */
  scoreValue: number;
  /** 创建时间 - 评分记录的创建时间 */
  createdAt: Date;
}

/**
 * 公示记录接口
 */
export interface IPublicationRecord extends BaseEntity {
  /** 公示记录ID - 公示记录的唯一标识符 */
  pubId: number;
  /** 任务ID - 关联的考核任务ID */
  taskId: number;
  /** 开始时间 - 公示的开始日期时间 */
  startTime: Date;
  /** 结束时间 - 公示的结束日期时间 */
  endTime: Date;
}

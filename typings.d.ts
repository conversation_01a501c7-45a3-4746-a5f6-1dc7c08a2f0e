import '@umijs/max/typings';
declare global {
  /** 综合基础路由与 umi 扩展路由 */
  type IBestAFSRoute = {
    path?: string;
    component?: string;
    name?: string;
    layout?: false;
    redirect?: string;
    keepQuery?: boolean;
    routes?: IBestAFSRoute[];
    wrappers?: Array<string>;
    /** 菜单图标 */
    icon?: string;
    /** 新页面打开 */
    target?: '_blank';
    /** 不展示顶栏 */
    headerRender?: boolean;
    /** 不展示页脚 */
    footerRender?: boolean;
    /** 不展示菜单 */
    menuRender?: boolean;
    /** 不展示菜单顶栏 */
    menuHeaderRender?: boolean;
    /** 权限配置，需要与 plugin-access 插件配合使用 */
    access?: string;
    /** 隐藏子菜单 */
    hideChildrenInMenu?: boolean;
    /** 隐藏自己和子菜单 */
    hideInMenu?: boolean;
    /** 在面包屑中隐藏 */
    hideInBreadcrumb?: boolean;
    /** 子项往上提，仍旧展示 */
    flatMenu?: boolean;
  };
  type InitialState = {
    currentUser?: {
      mobile?: string;
      userCode?: string;
      realName?: string;
      username?: string;
    };
    schoolInfo?: {
      id?: string;
      code?: string;
      name?: string;
    };
  };
  const TENCENT_COS: {
    SecretId: string;
    SecretKey: string /** 各项目一定要定义自己的目录，不要共用，a/b/ */;
    baseDir: string;
  };

  type ScoreDetail = {
    key: string;
    /** 观测点名称 */
    name: string;
    /** 实际得分 */
    realScore: number;
    /** 基础分 */
    score: number;
  };

  type EvaluationDetail = {
    key: string;
    /** 打分组名称 */
    role: string;
    /** 总成绩 */
    score: number;
    /** 权重 */
    weight: number;
    /** 是否是问卷 */
    isQuestion?: boolean;
    /** 详情 */
    details?: ScoreDetail[];
  };
}

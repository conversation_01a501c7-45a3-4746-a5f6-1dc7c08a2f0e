import { request } from '@umijs/max';

/** 查询赋分  GET /rule/:ruleId/scoring*/
export async function getAllEvaluationScoringAPI(ruleId: number | undefined) {
  return request<API.ResType<{ total?: number; list?: [] }>>(
    `/rule/${ruleId}/scoring`,
    {
      method: 'GET',
    },
  );
}

/** 创建赋分  POST /rule/:ruleId/scoring  */
export async function createEvaluationScoringAPI(
  ruleId: number | undefined,
  body: API.IScoring,
) {
  return request<API.ResType>(`/rule/${ruleId}/scoring`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新赋分  PUT /rule/:ruleId/scoring/:scoringId */
export async function updateEvaluationScoringAPI(
  ruleId: number | undefined,
  scoringId: number | undefined,
  body: Partial<API.IScoring>,
) {
  return request<API.ResType>(`/rule/${ruleId}/scoring/${scoringId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除赋分组  DELETE /rule/:ruleId/scoring/:scoringId */
export async function removeEvaluationScoringAPI(
  ruleId: number,
  scoringId: number,
) {
  return request<API.ResType<unknown>>(`/rule/${ruleId}/scoring/${scoringId}`, {
    method: 'DELETE',
  });
}

/**检查规则配置完整性 */
export async function checkEvaluationScoringAPI(
  ruleId: number,
  scoringId: number,
) {
  return request<API.ResType<unknown>>(
    `/rule/${ruleId}/scoring/${scoringId}/check `,
    {
      method: 'GET',
    },
  );
}

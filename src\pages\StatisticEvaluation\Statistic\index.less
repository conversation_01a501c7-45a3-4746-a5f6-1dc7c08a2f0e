.statisticWrapper {
  box-shadow: none !important;
  height: calc(100vh - 210px);
  overflow-y: auto;
  padding: 0 24px !important;

  .statisticCard {
    margin-bottom: 10px;

    .statisticCardTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 40px;

      > div {
        font-weight: bold;
      }
    }

    .statisticCardContent {
      border-radius: 16px;
      border: 2px solid #aeb9d5;
      background: #fff;
      overflow: hidden;
      padding: 16px;

      .subCard {
        width: calc(50% - 16px);
        border-radius: 12px;
        background: linear-gradient(180deg, #d1deff 0%, #dae4ff66 100%);
        color: #4a619f;
        line-height: 30px;
        font-size: 12px;

        .subCardTitle {
          font-size: 14px;
        }

        > div {
          padding: 24px;
          background: url('@/assets/total.png') no-repeat bottom right;
          background-size: 60px;
          background-position-y: calc(100% + 8px);
          background-position-x: calc(100% + 5px);

          > div:nth-child(2) {
            font-size: 20px;
          }
        }

        &:nth-child(2) {
          background: linear-gradient(180deg, #fff8c2 0%, #fffbda4d 100%);
          color: #8a7e22;

          > div {
            background-image: url('@/assets/done.png');
          }
        }

        &:nth-child(3) {
          background: linear-gradient(180deg, #dbf5ff 0%, #e6f7fe4d 100%);
          color: #2a5b6f;

          > div {
            background-image: url('@/assets/todo.png');
          }
        }

        &:nth-child(4) {
          background: linear-gradient(180deg, #dffed5 0%, #ecfee64d 100%);
          color: #5a8f4a;

          > div {
            background-image: url('@/assets/rate.png');
          }
        }
      }

      .chartPie {
        padding: 0 24px;

        p {
          font-weight: bold;
        }
      }

      .scoreExplain {
        border: 1px solid #aeb9d5;
        overflow: hidden;

        :global {
          .ant-pro-card-header {
            background: #f0f4ff;
            padding: 0 16px !important;
            border-bottom: 1px solid #aeb9d5;

            .ant-pro-card-title {
              line-height: 36px;
            }
          }

          .ant-pro-card-body {
            height: 192px;
            padding: 14px;

            p {
              margin: 0;
              font-weight: bold;
            }

            ul {
              list-style: disc;
              padding: 8px 0 0 16px;
              color: #767676;
              font-size: 12px;
              line-height: 30px;

              li {
                display: block;
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }
  }
}

.statisticHeader {
  margin: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

.statisticRecordDetail {
  :global {
    .ant-table-container {
      .ant-table-thead {
        padding: 4px 0;

        .ant-table-cell {
          background-color: #efefef;
        }
      }

      .ant-table-cell {
        padding: 0 8px !important;
        line-height: 30px;
      }

      table,
      thead,
      tbody,
      tr,
      td,
      th {
        border-color: #dadada !important;
      }
    }
  }
}

.scoreInfoModal {
  .score {
    line-height: 40px;

    span {
      color: var(--primary-color);
      font-weight: bold;
      font-size: 18px;
    }
  }
}

.fillingSituationHeader {
  margin: 12px;
  font-size: 20px;
  font-weight: 500;
  display: flex;
  justify-content: center;
}

.fillingSituationTable {
  padding: 0 12px;

  :global {
    .ant-pro-query-filter.ant-pro-query-filter {
      padding: 24px 0 12px;
    }
  }
}

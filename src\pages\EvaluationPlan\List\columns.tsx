import { ProColumns } from '@ant-design/pro-components';
import { Button, Popconfirm, Tag, Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';

type ColumnsType = {
  handEvent: (type: string, data: API.IAssessmentPlan) => void;
  loadingMap: Record<number, boolean>;
};

const columns = ({
  handEvent,
  loadingMap,
}: ColumnsType): ProColumns<
  API.IAssessmentPlan & { rulesStatus?: string }
>[] => {
  return [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
    },
    {
      title: '方案名称',
      align: 'center',
      dataIndex: 'planName',
      ellipsis: true,
    },
    {
      title: '适用学期',
      align: 'center',
      dataIndex: 'semesterName',
    },
    {
      title: '执行月份',
      align: 'center',
      valueType: 'date',
      render: (_, record) => {
        return `${dayjs(record?.startMonth).format('YYYY-MM')} 至 ${dayjs(
          record?.endMonth,
        ).format('YYYY-MM')}`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      align: 'center',
      valueType: 'date',
    },
    {
      title: '填写时段',
      align: 'center',
      dataIndex: 'fillableDates',
      render: (_, record) => {
        return record?.fillableDates?.length === 0 ? (
          '无限制'
        ) : record?.fillableDates?.length <= 5 ? (
          `每月 ${record?.fillableDates?.join(',')} 日`
        ) : (
          <Tooltip title={`每月 ${record?.fillableDates?.join(',')} 日`}>
            每月 {record?.fillableDates?.slice(0, 5)?.join(',') + '...'} 日
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      align: 'center',
      dataIndex: 'status',
      render: (_, record) => {
        return record?.status === 'published' ? (
          <Tag color="#87d068">启用</Tag>
        ) : (
          <Tag color="#999999">停用</Tag>
        );
      },
    },
    {
      title: '赋分形式',
      align: 'center',
      dataIndex: 'created_at',
      render: (_, record) => {
        return record?.scoringType === 'star' ? '星级评分' : '分值评分';
      },
    },
    {
      title: '规则检查',
      align: 'center',
      dataIndex: 'rules',
      render: (_dom, record) => {
        switch (record.rulesStatus) {
          case 'checking':
            return <span>检查中...</span>;
          case 'complete':
            return <Tag color="#108ee9">规则完整</Tag>;
          case 'incomplete':
            return <Tag color="#FFB833">规则不完整</Tag>;
          case 'error':
            return (
              <Button type="link" onClick={() => handEvent('retry', record)}>
                重新检查
              </Button>
            );
          default:
            return '-';
        }
      },
    },
    {
      title: '操作',
      key: 'option',
      align: 'center',
      render: (_dom, record) => {
        const showDelete = !(
          record.rulesStatus === 'complete' && record.status === 'published'
        );
        return (
          <>
            <Button
              size="small"
              type="link"
              key="rules"
              onClick={() => handEvent('rules', record)}
              disabled={
                !['complete', 'incomplete'].includes(
                  record?.rulesStatus as string,
                )
              }
            >
              设置被考核人员
            </Button>

            <Button
              size="small"
              type="link"
              key="edit"
              onClick={() => {
                handEvent('edit', record);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              key="remove"
              title={`您确定要${
                record?.status === 'published' ? '停用' : '启用'
              }此方案吗?`}
              description={
                record?.status === 'published' ? (
                  <>
                    <Typography.Paragraph>
                      1. 停用方案会清除
                      <Typography.Text mark>本月</Typography.Text>已产生的
                      <Typography.Text mark>考核填写记录</Typography.Text>；
                    </Typography.Paragraph>
                    <Typography.Paragraph>
                      2. 停用方案会清除
                      <Typography.Text mark>本月</Typography.Text>已产生的
                      <Typography.Text mark>公示信息</Typography.Text>；
                    </Typography.Paragraph>
                    <Typography.Paragraph>
                      3. 停用操作清除的数据
                      <Typography.Text type="danger">无法恢复</Typography.Text>
                      ，请谨慎操作！
                    </Typography.Paragraph>
                  </>
                ) : (
                  ''
                )
              }
              onConfirm={() => {
                handEvent('deactivateEnable', record);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                size="small"
                type="link"
                key="status"
                loading={loadingMap[record.planId]}
              >
                {record?.status === 'published' ? '停用' : '启用'}
              </Button>
            </Popconfirm>
            {showDelete && (
              <Popconfirm
                key="remove"
                title="您确定删除此方案吗?"
                description="删除后无法恢复"
                onConfirm={() => {
                  handEvent('remove', record);
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button size="small" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            )}
          </>
        );
      },
    },
  ];
};

export default columns;

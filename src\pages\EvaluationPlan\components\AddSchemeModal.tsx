import { getAllSemesterList } from '@/services/utils';
import { getCurrentXQ } from '@/utils/calc';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Checkbox, CheckboxChangeEvent, Col, message, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import GradeAchievement from './GradeAchievement';
import styles from './index.less';

const DateSelector: React.FC<{
  value?: number[];
  onChange?: (value: number[]) => void;
}> = ({ value = [], onChange }) => {
  // 创建内部状态
  const [selectedDays, setSelectedDays] = useState<number[]>(value);
  const allDays = Array.from({ length: 31 }, (_, i) => i + 1);

  useEffect(() => {
    setSelectedDays(value);
  }, [value]);

  // 计算全选和部分选择状态
  const isAllSelected = selectedDays.length === 31;
  const isPartialSelected = selectedDays.length > 0 && selectedDays.length < 31;

  // 处理全选/取消全选
  const handleCheckAllChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    const newSelectedDays = checked ? [...allDays] : [];
    setSelectedDays(newSelectedDays);
    if (onChange) {
      onChange(newSelectedDays);
    }
  };

  // 处理单个复选框变化
  const handleCheckboxChange = (day: number) => {
    return (e: CheckboxChangeEvent) => {
      const checked = e.target.checked;
      let newSelectedDays: number[];
      if (checked) {
        newSelectedDays = [...selectedDays, day].sort((a, b) => a - b);
      } else {
        newSelectedDays = selectedDays.filter((d) => d !== day);
      }
      setSelectedDays(newSelectedDays);
      if (onChange) {
        onChange(newSelectedDays);
      }
    };
  };

  return (
    <div className={styles.checkboxContainer}>
      <Checkbox
        indeterminate={isPartialSelected}
        checked={isAllSelected}
        onChange={handleCheckAllChange}
      >
        全选
      </Checkbox>
      <div className={styles.checkboxBody}>
        <Row gutter={[8, 8]}>
          {allDays.map((day) => (
            <Col span={4} key={day}>
              <Checkbox
                checked={selectedDays.includes(day)}
                onChange={handleCheckboxChange(day)}
              >
                {day}日
              </Checkbox>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

interface AddSchemeModalProps extends ModalFormProps {
  onCancel: () => void;
  data?: API.IAssessmentPlan & {
    rulesStatus?: 'complete' | 'incomplete' | 'error' | 'checking';
  };
}

export interface AddSchemeModalType {
  planId?: number;
  planName?: string;
  semester?: string;
  semesterName?: string;
  startMonth?: Date;
  endMonth?: Date;
  month?: string[];
  fillableDates?: number[];
  planStatus?: boolean;
}

const AddSchemeModal: React.FC<AddSchemeModalProps> = ({
  onCancel,
  data,
  ...restProps
}) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const readonly =
    data?.rulesStatus === 'complete' && data?.status === 'published';

  useEffect(() => {
    if (data && restProps.open) {
      const newData = {
        ...data,
        month: [data?.startMonth, data?.endMonth],
        planStatus: data?.fillableDates?.length > 0,
      };
      const timer = setTimeout(() => {
        if (formRef.current) {
          formRef.current.setFieldsValue(newData);
        }
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [data, restProps.open]);

  return (
    <>
      <ModalForm
        width={750}
        formRef={formRef}
        title={data ? '编辑考核方案' : '新增考核方案'}
        {...restProps}
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
          onCancel,
          styles: {
            body: {
              marginTop: '20px',
              maxHeight: '600px',
              overflowY: 'auto',
              overflowX: 'hidden',
              paddingRight: '5px',
            },
          },
        }}
      >
        <ProFormText hidden name="planId" />
        <ProFormText hidden name="semesterName" />
        <ProFormText hidden name="startMonth" />
        <ProFormText hidden name="endMonth" />
        <ProFormText
          name="planName"
          label="方案名称"
          fieldProps={{
            maxLength: 100,
          }}
          rules={[{ required: true, message: '方案名称是必填项' }]}
          placeholder="请输入"
        />
        <Row gutter={16}>
          <Col span={12}>
            <ProFormSelect
              name="semester"
              label="学年学期"
              readonly={readonly}
              rules={[{ required: true, message: '请选择学年学期' }]}
              request={async () => {
                const {
                  errCode,
                  data: semesterList,
                  msg,
                } = await getAllSemesterList({
                  enterpriseCode: schoolInfo?.code || '',
                });
                if (errCode) {
                  message.warning('获取学年学期失败，' + msg);
                  return [];
                }
                if (!semesterList?.list || semesterList.list.length < 0) {
                  return [];
                }
                if (!data) {
                  const curTerm = getCurrentXQ(semesterList.list);
                  if (curTerm) {
                    formRef.current?.setFieldsValue({
                      semester: curTerm?.semester_code,
                      semesterName: `${curTerm?.semester_code?.substring(
                        0,
                        4,
                      )}-${curTerm?.semester_code?.substring(4, 8)} ${
                        curTerm?.semester_name
                      }`,
                    });
                  }
                }

                return (semesterList?.list ?? [])?.map((item: any) => ({
                  label: `${item.semester_code?.substring(
                    0,
                    4,
                  )}-${item.semester_code?.substring(4, 8)}
                             ${item.semester_name}`,
                  value: item.semester_code,
                }));
              }}
              onChange={(
                _,
                option:
                  | { label: string; value: string }
                  | { label: string; value: string }[]
                  | undefined,
              ) => {
                if (formRef.current && option && !Array.isArray(option)) {
                  formRef.current.setFieldsValue({
                    semesterName: option?.label,
                  });
                }
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormDateRangePicker
              name="month"
              label="执行月份"
              placeholder="请输入"
              fieldProps={{
                picker: 'month',
                format: 'YYYY-MM',
                disabledDate: (current) => {
                  if (!current) return false;
                  const currentMonth = dayjs().startOf('month');
                  return current.isBefore(currentMonth);
                },
                onChange: (_, dateString) => {
                  if (formRef.current && dateString) {
                    formRef.current.setFieldsValue({
                      startMonth: dateString[0],
                      endMonth: dateString[1],
                    });
                  }
                },
              }}
              rules={[{ required: true, message: '执行月份是必填项' }]}
            />
          </Col>
        </Row>

        <ProFormRadio.Group
          name="planStatus"
          label="填写时段"
          initialValue={true}
          options={[
            {
              label: '整月',
              value: false,
            },
            {
              label: '自定义',
              value: true,
            },
          ]}
          rules={[{ required: true, message: '填写时段是必选项' }]}
        />

        <ProFormDependency name={['planStatus']}>
          {({ planStatus }) => {
            if (planStatus) {
              return (
                <ProForm.Item
                  name="fillableDates"
                  label="选择日期"
                  rules={[{ required: true, message: '请选择日期' }]}
                  tooltip="注意：系统会根据选择的月份自动校验日期有效性。例如：2月最多28天（闰年29天），4月、6月、9月、11月最多30天，其他月份最多31天。超出当月天数的选择将自动失效。"
                >
                  <DateSelector />
                </ProForm.Item>
              );
            }
            return null;
          }}
        </ProFormDependency>
        <Row>
          <Col span={12}>
            <ProFormRadio.Group
              readonly={readonly}
              initialValue={'score'}
              hidden
              width="xl"
              name="scoringType"
              label="赋分形式"
              options={[
                {
                  label: '分值',
                  value: 'score',
                },
                {
                  label: '星级',
                  value: 'star',
                },
              ]}
              rules={[{ required: true, message: '赋分形式是必选项' }]}
            />
          </Col>
          <Col span={12}>
            <ProFormDependency name={['scoringType']}>
              {({ scoringType }) => {
                return (
                  <>
                    {scoringType === 'score' && (
                      <ProFormRadio.Group
                        name="publicationType"
                        hidden
                        initialValue={'score'}
                        label="评分类型"
                        layout="horizontal"
                        options={[
                          {
                            label: '展示分数',
                            value: 'score',
                          },
                          // {
                          //   label: '展示等次',
                          //   value: 'grade',
                          // },
                        ]}
                        rules={[
                          { required: true, message: '评分类型是必选项' },
                        ]}
                      />
                    )}
                    {scoringType === 'star' && (
                      <ProFormDigit
                        label="最大星级"
                        name="maxStars"
                        min={1}
                        max={10}
                        initialValue={5}
                        fieldProps={{ precision: 0, suffix: '星' }}
                        rules={[
                          { required: true, message: '最大星级是必填项' },
                        ]}
                      />
                    )}
                  </>
                );
              }}
            </ProFormDependency>
          </Col>
        </Row>
        <ProFormDependency name={['scoringType', 'publicationType']}>
          {({ scoringType, publicationType }) => {
            return (
              <>
                {scoringType === 'score' && publicationType === 'grade' && (
                  <ProForm.Item
                    name="gradeAchievement"
                    label="等次设置"
                    tooltip="设置时请按照从上至下依次递减顺序进行配置，以便更好地进行等次划分"
                    valuePropName="value"
                    trigger="onChange"
                  >
                    <GradeAchievement className={styles.gradeAchievement} />
                  </ProForm.Item>
                )}
                {scoringType === 'score' && publicationType === 'score' && (
                  <Row gutter={16}>
                    <Col span={12}>
                      <ProFormDigit
                        initialValue={0}
                        label="上浮"
                        name="maxAdjustment"
                        min={0}
                        max={10}
                        fieldProps={{ precision: 0 }}
                        tooltip="请设置打分的上浮，例如：当上浮设置 2 分时， 某一个打分项的基础分为 10 分，则最多可以打12分。"
                        rules={[
                          { required: true, message: '最大调整值是必填项' },
                        ]}
                      />
                    </Col>
                    <Col span={12}>
                      <ProFormDigit
                        initialValue={0}
                        label="下浮"
                        name="minAdjustment"
                        tooltip="请设置打分的下浮，例如：当下浮设置 2 分时， 某一个打分项的基础分为 10分，则最小可以打8分。"
                        min={0}
                        max={10}
                        fieldProps={{ precision: 0 }}
                        rules={[
                          { required: true, message: '最小调整值是必填项' },
                        ]}
                      />
                    </Col>
                  </Row>
                )}
              </>
            );
          }}
        </ProFormDependency>
      </ModalForm>
    </>
  );
};
export default AddSchemeModal;

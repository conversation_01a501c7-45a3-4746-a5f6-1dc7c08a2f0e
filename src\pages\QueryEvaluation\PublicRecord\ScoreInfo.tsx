import ReportDetails from '@/components/ReportDetails';
import { Button, Empty, Modal } from 'antd';
import styles from './index.less';

// 分数详情弹窗组件
const ScoreInfo = ({
  modalData,
  handleCancel,
}: {
  modalData: {
    isModalOpen: boolean;
    detail?: API.IScoreReport;
  };
  handleCancel: () => void;
}) => {
  return (
    <Modal
      title="得分详情"
      open={modalData.isModalOpen}
      width={800}
      onCancel={handleCancel}
      className={styles.scoreInfoModal}
      footer={[
        <Button key="back" onClick={handleCancel}>
          确定
        </Button>,
      ]}
      styles={{
        body: {
          maxHeight: '70vh',
          overflowY: 'auto',
          padding: '0 24px',
        },
      }}
    >
      {/* <div className={styles.score}>
        考核总分：<span>{mockData?.personalDetail?.score}</span>
      </div> */}
      {!!modalData.isModalOpen && !!modalData.detail ? (
        <ReportDetails info={modalData.detail} />
      ) : (
        <Empty description="该记录暂无详情" />
      )}
    </Modal>
  );
};

export default ScoreInfo;
